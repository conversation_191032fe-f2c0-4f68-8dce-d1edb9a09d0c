specVersion: "3.0"
metadata:
  name: com.sap.calm.imp.tkm.ui
type: application
builder:
  resources:
    excludes:
      - "/test/**"
      - "/localService/**"
      - "/gen/**"
  settings:
    includeDependency:
      - "x-shared-ui-lib"
  customTasks:
    - name: ui5-tooling-transpile-task
      afterTask: replaceVersion
resources:
  configuration:
    paths:
      webapp: webapp
server:
  settings:
    httpPort: 3000
    httpsPort: 3000
  customMiddleware:
    - name: tkm-ui-static-proxy
      beforeMiddleware: compression
    - name: ui5-middleware-dwc
      beforeMiddleware: compression
      configuration:
        remoteUrl: "https://jupiter-calm-dev-eu10-004.cfapps.eu10-004.hana.ondemand.com"
        skipAuth: false
        automateLogin: false
        launchpadAsDefault: true
#        tenant: "calm-dev-eu10-004-relcallstablefeatures-team-ptm-fortester"
        tenant: "calm-dev-eu10-004-relcallstablefeatures-1stabledata"
        useLocalProxy: false
    - name: ui5-fall-through-proxy-dwc
      beforeMiddleware: nonReadRequests
    - name: ui5-tooling-transpile-middleware
      afterMiddleware: compression

---
# TKM Custom Middleware
specVersion: "3.0"
kind: extension
type: server-middleware
metadata:
  name: tkm-ui-static-proxy
middleware:
  path: TKMLocalStaticProxy.js
