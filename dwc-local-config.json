{"toggles": [{"name": "imp_tkm_xxx", "activated": false}, {"name": "imp_tkm_ftrFromDft_toggle", "activated": true}, {"name": "imp_pjm_servOrdLang_toggle", "activated": true}, {"name": "imp_tkm_commentNotif_toggle", "activated": true}, {"name": "imp_tkm_warnOnTeamSwitchList_toggle", "activated": true}, {"name": "imp_tkm_reqAIFit2StandardPoC_toggle", "activated": true}], "scopes": [], "proxy": [{"inboundRuleConfigPath": "./imp-tkm-taskservice-ui.v1.yaml", "host": "http://localhost:8081"}, {"inboundRuleConfigPath": "./imp-pjm-projectservice-ui.v1.yaml", "host": "http://localhost:8082"}], "headers": []}