import UI5Object from "sap/ui/base/Object";
import ODataModel from "sap/ui/model/odata/v4/ODataModel";
import Sorter from "sap/ui/model/Sorter";
import Filter from "sap/ui/model/Filter";
import FilterOperator from "sap/ui/model/FilterOperator";
import Constant from "com/sap/calm/imp/tkm/ui/model/Constant";
import DwcHandler from "com/sap/calm/imp/tkm/ui/handler/DwcHandler";
import ShellContainer from "sap/ushell/Container";
import OperationMode from "sap/ui/model/odata/OperationMode";
import Context from "sap/ui/model/odata/v4/Context";
import { CBLD_C_USER } from "../gen/pjm/CbldProjectSrvModel";

// --- Constants ---
const DOWNLOAD_SELECTED_TASK_URL =
    "/ui/task-management-service-ui/v1/api/v1/excel/downloadSelectedTask/";
const DOWNLOAD_CHECKLIST_URL = "/ui/task-management-service-ui/v1/api/v1/excel/downloadChecklist/";
const UPLOAD_TASKS_URL = "/ui/task-management-service-ui/v1/api/v1/excel/assuredCreate/";
const UPLOAD_CHECKLIST_URL = "/ui/task-management-service-ui/v1/api/v1/excel/uploadChecklist/";
const AI_CREATE_REQUIREMENT_URL = "/ui/task-management-service-ui/v1/api/v1/ai/create-requirement";
const TASKS_SERVICE_URL = "/CBLD_C_TASK_TP";
const FEATURES_SERVICE_URL = "/Features";
const DOWNLOAD_ONCE_MAX_COUNT = 2000;
const DOWNLOAD_TOTAL_MAX_COUNT = 100000;

const constant = Constant.getInstance();

// --- Interfaces ---
interface TKMComponent {
    getModel: (name?: string) => ODataModel;
    oErrorHandler: { showErrorMessageBox: (msg: string) => void };
    tileInfo?: { id: string };
}

type QueryOption = Record<string, any>;
type TaskFilter = { search?: string; filter?: Filter };
type BusyModel = { setProperty: (path: string, value: any) => void };

let instance: DataHandler | null = null;

export default class DataHandler extends UI5Object {
    public static getInstance(): DataHandler {
        if (!instance) {
            throw new Error("DataHandler is not initialized. Call constructor first.");
        }
        return instance;
    }

    private readonly taskModel: ODataModel;
    private readonly projectModel: ODataModel;
    private readonly currentTileId: string;
    private readonly errorHandler: { showErrorMessageBox: (msg: string) => void };

    private getProjectInfoPromises?: Record<string, Promise<any>>;
    private cachedKanbanContexts?: Context[];
    private taskWorkStreamsPromise?: Promise<any>;
    private featureOdataModel?: ODataModel;
    private processODataModel?: ODataModel;
    private processHierarchyODataModel?: ODataModel;
    private libraryODataModel?: ODataModel;
    private documentODataModel?: ODataModel;

    constructor(component: TKMComponent) {
        super();

        this.currentTileId = component.tileInfo?.id ?? "";
        this.errorHandler = component.oErrorHandler;

        this.taskModel = component.getModel();
        this.taskModel.setSizeLimit(10000);
        this.projectModel = component.getModel(constant.JSON_MODELS.filter);

        instance = this;
    }

    public initODataService(url: string): ODataModel {
        return new ODataModel({
            serviceUrl: url,
            synchronizationMode: "None",
            operationMode: OperationMode.Server,
            httpHeaders: {
                "x-csrf-token": (window as any).dwc.csrf.csrfToken,
            },
            groupId: "$direct",
        });
    }

    private appendSearchParameter(queryOption: QueryOption, searchText?: string): void {
        if (searchText) {
            queryOption.$search = searchText;
        }
    }

    public getProjectInfo(projectGuid: string, forceRefresh = false): Promise<any> {
        if (!this.getProjectInfoPromises?.[projectGuid] || forceRefresh) {
            this.getProjectInfoPromises = {};
            this.getProjectInfoPromises[projectGuid] = this.projectModel
                .bindContext(`/CBLD_C_PROJECT_TP(${projectGuid})`, undefined, {
                    $expand:
                        "to_solutions,to_typeDetail($expand=to_phases),to_milestones($expand=to_phase;$orderby=ms_order),to_teams($expand=to_members,to_role),to_crews,to_proj_roles($expand=to_mem_role),to_companies,to_landscapes,to_timeboxes($orderby=start_date,end_date,name),to_cbcProjects,to_release($expand=to_versions,to_system_groups($expand=to_content($expand=to_system))),to_subscriptions,to_constraints",
                })
                .requestObject();
        }
        return this.getProjectInfoPromises[projectGuid];
    }

    public refreshCBCProjectTasks(projectId: string): Promise<any> {
        const contextBinding = this.projectModel.bindContext("/REFRESH_CBC_TASKS(...)", undefined, {
            $$updateGroupId: "APIGroup",
        });
        contextBinding.setParameter("PROJECT_GUID", projectId);
        return contextBinding.execute().catch(() => {});
    }

    public getTags(): Promise<any[]> {
        return this.taskModel
            .bindList("/CBLD_C_TAG_W_GROUP")
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    public getContentPackages(): Promise<any[]> {
        return this.taskModel
            .bindList("/CBLD_C_CONTENT_PACKAGE")
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    public getFilteredTasks(taskFilter: TaskFilter, taskSorter: Sorter): Promise<any[]> {
        const queryOption: QueryOption = {
            $select: "guid,short_text,source_v2,type_id_v2,approval_state_id,state",
            $expand: "to_parentTask",
        };
        this.appendSearchParameter(queryOption, taskFilter.search);
        return this.taskModel
            .bindList(TASKS_SERVICE_URL, undefined, taskSorter, taskFilter.filter, queryOption)
            .requestContexts();
    }

    public getGanttChartTasks(taskFilter: TaskFilter): Promise<any[]> {
        const queryOption: QueryOption = {
            $select:
                "guid,formatted_universal_id,short_text,status_text_v2,sub_status_id,deliverable_guid,ex_project_phase_id,type_id_v2,source_v2,start_date,due_date,start_date_time,due_date_time,approval_state_id,status_id_v2,si_global_id,state,planned_effort,story_point,processor,processor_name,ex_start_date_source,ex_due_date_source,precise_time_mode,ex_cias_solution_id",
            $expand: "to_successor,to_parentTask",
        };
        this.appendSearchParameter(queryOption, taskFilter.search);
        const sorters = [
            new Sorter("short_text", false),
            new Sorter("source_order_v2", false),
            new Sorter("ex_sequence_id", false),
            new Sorter("ex_company_name", false),
            new Sorter("created_timestamp", false),
        ];
        return this.taskModel
            .bindList(TASKS_SERVICE_URL, undefined, sorters, taskFilter.filter, queryOption)
            .requestContexts()
            .then((contexts: any[]) => this.getBindingContextObjects(contexts));
    }

    public getKanbanTasks(taskFilter: TaskFilter): Promise<any[]> {
        const selects = [
            "guid",
            "short_text",
            "priority_v2",
            "state",
            "source_v2",
            "type_id_v2",
            "formatted_universal_id",
            "due_date",
            "due_date_time",
            "precise_time_mode",
            "status_id_v2",
            "processor",
            "processor_name",
            "ex_timebox_guid",
            "ex_project_phase_id",
            "ex_due_date_link",
            "ex_due_date_source",
            "sub_status_id",
            "approval_state_id",
        ];
        const queryOption: QueryOption = { $select: selects.join(",") };
        if (taskFilter.search) {
            queryOption.$search = taskFilter.search;
        }
        return this.taskModel
            .bindList(TASKS_SERVICE_URL, undefined, undefined, taskFilter.filter, queryOption)
            .requestContexts()
            .then((contexts: any[]) => {
                this.cachedKanbanContexts = contexts;
                return this.getBindingContextObjects(contexts);
            });
    }

    public updateKanbanItem(taskGuid: string, property: string, value: any): Promise<any> {
        const targetContext = this.cachedKanbanContexts?.find(
            (context: any) => context.getProperty("guid") === taskGuid,
        );
        if (targetContext) {
            return targetContext.setProperty(property, value);
        }
        return this.updateTaskProperties(taskGuid, [{ name: property, value }]);
    }

    public getFilteredTasks4AcceleratorView(taskFilter: TaskFilter): Promise<any[]> {
        const queryOption: QueryOption = {
            $select: "guid,ex_project_phase_id",
            $expand: "to_accelerators($expand=to_acceleratorDetail)",
        };
        this.appendSearchParameter(queryOption, taskFilter.search);
        return this.taskModel
            .bindList(TASKS_SERVICE_URL, undefined, undefined, taskFilter.filter, queryOption)
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    private getFilteredTasksWithPaging(
        taskSorter: Sorter,
        taskFilter: TaskFilter,
        queryOption: QueryOption,
        skip: number,
        top: number,
    ): Promise<any[]> {
        this.appendSearchParameter(queryOption, taskFilter.search);
        return this.taskModel
            .bindList(TASKS_SERVICE_URL, undefined, taskSorter, taskFilter.filter, queryOption)
            .requestContexts(skip, top);
    }

    public async getFilteredTasks4Export(
        taskFilter: TaskFilter,
        taskSorter: Sorter,
        downloadTemplate: boolean,
        downloadWithPaging: boolean,
    ): Promise<any[]> {
        if (downloadTemplate) {
            return this.getFilteredTasksWithPaging(
                taskSorter,
                taskFilter,
                { $select: "guid" },
                0,
                DOWNLOAD_ONCE_MAX_COUNT,
            );
        }
        const bindParams = {
            $select:
                "short_text,formatted_universal_id,start_date,due_date,ex_project_phase_id,responsible_user_type,processor,processor_name,responsible_proj_role,responsible_crew,ex_company_name,target_system,action_uri,ex_company_id,deliverable_text,deliverable_order,ex_workstream,ex_timebox_guid,ex_cbc_project_name,last_changed_by,last_changed_by_name,last_changed_timestamp,story_point,type_id_v2,source_v2,status_id_v2,type_text_v2,priority_v2,source_text_v2,source_order_v2,status_text_v2,status_order_v2,si_global_id,si_title,approval_state_id,sub_status_id,release_version_name,release_version_start_date,release_version_end_date,created_by,created_by_name,created_timestamp,productions,state,planned_effort,start_date_time,due_date_time,precise_time_mode,duration,close_timestamp",
            $expand:
                "to_texts,to_notes($orderby=created_timestamp desc),to_logs_v2($filter=entity_name ne 'TaskLongText';$orderby=changed_on desc),to_tag_assignments($orderby=tag_label asc),to_attachments($orderby=created_timestamp desc),to_stakeholders,to_entity_relations,to_external_references",
        };
        let contexts = await this.getFilteredTasksWithPaging(
            taskSorter,
            taskFilter,
            bindParams,
            0,
            DOWNLOAD_ONCE_MAX_COUNT,
        );
        const totalContexts = [...contexts];
        if (downloadWithPaging) {
            while (
                contexts.length === DOWNLOAD_ONCE_MAX_COUNT &&
                totalContexts.length < DOWNLOAD_TOTAL_MAX_COUNT
            ) {
                // Here "await" in loop can not be avoid since whether issuing a new call dependents on the last response of previous call
                /* eslint-disable no-await-in-loop */
                contexts = await this.getFilteredTasksWithPaging(
                    taskSorter,
                    taskFilter,
                    bindParams,
                    totalContexts.length,
                    DOWNLOAD_ONCE_MAX_COUNT,
                );
                totalContexts.push(...contexts);
            }
        }
        return totalContexts;
    }

    public getTaskPredecessor(taskGuid: string): Promise<any[]> {
        return this.taskModel
            .bindList("/CBLD_C_TASK_PREDECESSOR", undefined, undefined, [
                new Filter({
                    path: "task_1st",
                    operator: FilterOperator.EQ,
                    value1: taskGuid,
                }),
            ])
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    public getTagsOfTask(taskGuid: string): Promise<any[]> {
        return this.taskModel
            .bindList("/CBLD_C_TASK_TAG_ASSIGNMENT", undefined, undefined, [
                new Filter({
                    filters: [
                        new Filter({
                            path: "task_guid",
                            operator: FilterOperator.EQ,
                            value1: taskGuid,
                        }),
                    ],
                    and: true,
                }),
            ])
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    public getTaskCountByPhase(): Promise<any[]> {
        return this.taskModel
            .bindList("/CBLD_C_TASK_COUNT_BY_PHASE")
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    public updateTasksBatch(tasksKey: string[], changeProperties: any[]): Promise<any> {
        const contextBinding = this.taskModel.bindContext("/MASS_UPDATE_TASKS(...)", undefined, {
            $$updateGroupId: "MassEditGroup",
        });
        contextBinding
            .setParameter("TASK_GUIDS", tasksKey)
            .setParameter("CHANGED_PROPERTIES", changeProperties);
        return contextBinding.execute();
    }

    public moveItemsToTargetProject(
        taskGuids: string[],
        targetProjectGuid: string,
        targetScopeGuid?: string,
    ): Promise<any> {
        const contextBinding = this.taskModel.bindContext("/MOVE_TASKS(...)", undefined, {
            $$updateGroupId: "MoveTaskGroup",
        });
        const params = {
            TASK_GUIDS: taskGuids,
            TARGET_PROJECT_GUID: targetProjectGuid,
            TARGET_SCOPE_GUID: targetScopeGuid || null,
        };
        contextBinding.setParameter("MOVE_TASKS_PARAMS", params);
        return contextBinding.execute();
    }

    public createTask(task: any, busyModel?: BusyModel): Promise<any> {
        return this.createTaskModelEntity(TASKS_SERVICE_URL, task, busyModel);
    }

    public createSubTasks(children: any[], busyModel?: BusyModel): Promise<any[]> {
        return Promise.all(children.map((child) => this.createTask(child, busyModel)));
    }

    public updateSubTasks(children: any[], isChecklist = false): Promise<any[]> {
        return Promise.all(
            children.map((child) => {
                const changeProperties = this.changedPropertiesForChild(child.type_id_v2).map(
                    (property) => ({
                        name: property,
                        value: child[property],
                    }),
                );
                return this.updateTaskProperties(
                    child.task_guid,
                    changeProperties,
                    isChecklist ? "ChecklistGroup" : "APIGroup",
                );
            }),
        );
    }

    public deleteSubTasks(childrenGuids: string[]): Promise<any> {
        if (!Array.isArray(childrenGuids) || childrenGuids.length === 0) {
            return Promise.resolve();
        }
        return this.batchDeleteTasksWithUserOption(childrenGuids, false);
    }

    private changedPropertiesForChild(typeId: string): string[] {
        return typeId === constant.TASK_MASTER_TYPE.subTaskV2
            ? [
                  "short_text",
                  "status_id_v2",
                  "due_date",
                  "due_date_time",
                  "ex_due_date_link",
                  "ex_due_date_source",
                  "responsible_crew",
                  "processor",
              ]
            : [
                  "short_text",
                  "sub_status_id",
                  "due_date",
                  "due_date_time",
                  "ex_due_date_link",
                  "ex_due_date_source",
                  "responsible_user_type",
                  "processor",
                  "ex_response",
                  "responsible_crew",
              ];
    }

    public updateTaskDescription(
        taskDescription: any,
        groupId = "DescriptionGroup",
    ): Promise<any[]> {
        const context = this.taskModel
            .bindContext(`/CBLD_C_TASK_TEXT_TP(${taskDescription.guid})`, undefined, {
                $$updateGroupId: groupId,
            })
            .getBoundContext();
        return Promise.all([
            context.setProperty("text", taskDescription.text),
            this.taskModel.submitBatch(groupId),
        ]);
    }

    public createTaskDescription(taskDescription: any): Promise<any> {
        return this.createTaskModelEntity("/CBLD_C_TASK_TEXT_TP", taskDescription);
    }

    public createTaskComment(taskNote: any): Promise<any> {
        return this.createTaskModelEntity("/CBLD_C_TASK_NOTE_TP", taskNote);
    }

    public updateTaskComment(taskNote: any): Promise<any> {
        const context = this.taskModel
            .bindContext(`/CBLD_C_TASK_NOTE_TP(${taskNote.guid})`)
            .getBoundContext();
        return context.setProperty("text", taskNote.text);
    }

    private createTaskModelEntity(
        path: string,
        entityContent: any,
        busyModel?: BusyModel,
    ): Promise<any> {
        const listBinding = this.taskModel.bindList(path);
        const context = listBinding.create(entityContent, true, false);

        listBinding.attachCreateCompleted(() => {
            // In case of bad request, promise of created() will not reject (https://sapui5.hana.ondemand.com/#/topic/c9723f8265f644af91c0ed941e114d46)
            busyModel?.setProperty?.("/busy", false);
        });
        return context.created()?.then(() => context.getObject()) || Promise.resolve();
    }

    public createTag(entityContent: any, busyModel?: BusyModel): Promise<any> {
        return this.createTaskModelEntity("/CBLD_C_TAG", entityContent, busyModel);
    }

    public createTaskRelation(taskRelation: any, busyModel?: BusyModel): Promise<any> {
        return this.createTaskModelEntity("/CBLD_C_TASK_RELATION_TP", taskRelation, busyModel);
    }

    public deleteTaskRelation(taskRelationGuid: string): Promise<any> {
        const context = this.taskModel
            .bindContext(`/CBLD_C_TASK_RELATION_TP(${taskRelationGuid})`)
            .getBoundContext();
        return context.requestObject().then(() =>
            context.delete("$auto").catch((error: any) => {
                throw this.parseError(error.responseText).error.message.value;
            }),
        );
    }

    public getDynamicFilterOptions(projectGuid: string, currentTileId: string): Promise<any> {
        return this.execFucImpForSingleProject(
            projectGuid,
            currentTileId,
            "/GET_PROJECT_CONTEXT_4_FILTER_V2(...)",
        );
    }

    public getTaskDeliverableDescription(deliverableGuid: string): Promise<any> {
        return this.taskModel
            .bindContext(`/CBLD_C_TASK_DELIVERABLE_TMPT(${deliverableGuid})`)
            .requestObject();
    }

    public getCalmRelationEntityId(relationGuid: string): Promise<any> {
        return this.taskModel
            .bindContext(`/CBLD_C_ENTITY_RELATION(${relationGuid})`)
            .requestObject()
            .then((data: any) => data.entity_id);
    }

    public getTaskDeliverables(projectGuid: string): Promise<any[]> {
        const contextBinding = this.taskModel.bindList(
            "/CBLD_C_TASK_DELIVERABLE_TP",
            undefined,
            undefined,
            [
                new Filter({
                    path: "project_guid",
                    operator: FilterOperator.EQ,
                    value1: projectGuid,
                }),
            ],
            { $select: "guid,short_text,is_closed,project_guid" },
        );
        return contextBinding.requestContexts().then(this.getBindingContextObjects);
    }

    public getTaskWorkstreams(isForceRefresh?: boolean): Promise<any> {
        if (!this.taskWorkStreamsPromise || isForceRefresh) {
            const contextBinding = this.taskModel.bindContext(
                "/CBLD_C_ENTITY_ALL_WORKSTREAM",
                undefined,
                // @ts-ignore
                { $orderby: "name" }, // $orderby is not mentioned in API, but it actually works
            );
            this.taskWorkStreamsPromise = contextBinding.requestObject();
        }
        return this.taskWorkStreamsPromise;
    }

    public getCurrentUserInfo(): Promise<any> {
        return ShellContainer.getServiceAsync("UserInfo");
    }

    public getUserAuth(): Promise<any> {
        const functionImportName = "/CHECK_OPERATION_AUTH_ENH(...)";
        const contextBinding = this.taskModel.bindContext(functionImportName);
        return contextBinding.execute().then(() => contextBinding.getBoundContext().getObject());
    }

    public getProjectUserAuth(): Promise<any> {
        const functionImportName = "/CHECK_OPERATION_AUTH_ENH(...)";
        const contextBinding = this.projectModel.bindContext(functionImportName);
        return contextBinding.execute().then(() => contextBinding.getBoundContext().getObject());
    }

    public batchDeleteTasksWithUserOption(
        taskGuids: string[],
        isDeletedAssociatedTask: boolean,
    ): Promise<any> {
        const functionImportName = "/MASS_DELETE_TASKS_WITH_CHILDREN(...)";
        const contextBinding = this.taskModel.bindContext(functionImportName);
        contextBinding
            .setParameter("TASK_GUIDS", taskGuids)
            .setParameter("DELETE_CHILDREN", isDeletedAssociatedTask);
        return contextBinding.execute();
    }

    public getSolutions(): Promise<any[]> {
        const contextBinding = this.projectModel.bindContext("/GET_ALL_AVAILABLE_TEMPLATES(...)");
        return contextBinding
            .execute()
            .then(() => contextBinding.getBoundContext().getObject().value);
    }

    public triggerForceUpdate(): void {
        // independent non-batch request and ignore the result (for better performance)
        // Force update of activate roadmaps
        this.projectModel
            .bindContext("/FORCE_UPDATE(...)", undefined, { $$groupId: "$direct" })
            .execute()
            .catch(() => {});
        // Force update of template checklist items
        this.taskModel
            .bindContext("/REFRESH_TEMPLATE_CHECKLIST_ITEMS(...)", undefined, {
                $$groupId: "$direct",
            })
            .execute()
            .catch(() => {});
    }

    public submitTaskChange(groupId = "APIGroup"): Promise<any> {
        return this.taskModel.submitBatch(groupId);
    }

    private parseError(jsonStr: string): any {
        let response;
        try {
            response = JSON.parse(jsonStr);
        } catch (e) {
            response = {
                error: {
                    message: {
                        value: jsonStr,
                    },
                },
            };
        }
        return response;
    }

    public getAllProjects(): Promise<any[]> {
        if (DwcHandler.isCalmContextExist()) {
            return Promise.resolve(DwcHandler.getAllProjects());
        }
        return this.projectModel
            .bindList("/CBLD_C_PROJECT_TP")
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    public getCbcConfiguration(): Promise<any[]> {
        return this.projectModel
            .bindList("/CBLD_C_CBC_SERVICE_OBJECT", undefined, undefined, undefined, {
                $select: "main_url,tenant_name,tenant_id",
            })
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    public getCalmContext(selectedProjectGuid: string): Promise<any[]> {
        this.triggerForceUpdate();

        return Promise.all([
            this.getCurrentUserInfo(),
            this.getProjectInfo(selectedProjectGuid),
            this.getUserAuth(),
            this.getDynamicFilterOptions(selectedProjectGuid, this.currentTileId),
            this.getTaskWorkstreams(),
            this.getAllProjects(),
            this.getCbcConfiguration(),
            this.getSolutions(),
            this.getTags(),
            this.getContentPackages(),
            this.getProjectUserAuth(),
            this.getTaskCountByPhase(),
            this.getTaskCountByReleaseVersion(),
            this.getTaskDeliverables(selectedProjectGuid),
        ]).catch((error) => Promise.reject(new Error(error.message)));
    }

    public getBindingContextObjects(bindingContexts: any[]): any[] {
        return bindingContexts.map((context) => context.getObject());
    }

    public downloadSelectedTask(
        projectGuid: string,
        taskGuids: string[],
        csrfToken: string,
        creating: boolean,
    ): Promise<Response> {
        return fetch(DOWNLOAD_SELECTED_TASK_URL + projectGuid, {
            method: "POST",
            body: JSON.stringify({
                taskGuids: taskGuids,
                isForUpdate: !creating,
            }),
            headers: {
                "Content-Type": "application/json;charset=UTF-8",
                "x-csrf-token": csrfToken,
            },
        });
    }

    public downloadChecklist(
        qGateGuid: string,
        selectedChecklistGuids: string[],
        csrfToken: string,
    ): Promise<Response> {
        return fetch(DOWNLOAD_CHECKLIST_URL + qGateGuid, {
            method: "POST",
            body: JSON.stringify(selectedChecklistGuids),
            headers: {
                "Content-Type": "application/json;charset=UTF-8",
                "x-csrf-token": csrfToken,
            },
        });
    }

    private _ajaxPost(url: string, formData: FormData, csrfToken: string): Promise<any> {
        return fetch(url, {
            method: "POST",
            headers: {
                "x-csrf-token": csrfToken,
            },
            body: formData,
        })
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`${response.status} : ${response.statusText}`);
                }
                return response.json();
            })
            .then((data) => ({
                data: data,
                textStatus: "success",
                jqXHR: null,
            }))
            .catch((error) => {
                this.errorHandler?.showErrorMessageBox(error.message);
            });
    }

    public persistCachedManualTasks(
        projectGuid: string,
        csrfToken: string,
        file: File,
    ): Promise<any> {
        const formData = new FormData();
        formData.append("file", file);
        return this._ajaxPost(
            `${UPLOAD_TASKS_URL + projectGuid}/dummyCacheId`,
            formData,
            csrfToken,
        );
    }

    public uploadAndPersistChecklist(
        qGateGuid: string,
        csrfToken: string,
        file: File,
    ): Promise<any> {
        const formData = new FormData();
        formData.append("file", file);
        formData.append("isSave", "true");
        return this._ajaxPost(UPLOAD_CHECKLIST_URL + qGateGuid, formData, csrfToken);
    }

    public createTransientTask(transientTask: any): Context {
        const newCreatedTaskContext = this.taskModel
            .bindList(TASKS_SERVICE_URL, undefined, undefined, undefined, {
                $$updateGroupId: "APIGroup",
            })
            .create(transientTask);

        newCreatedTaskContext.created()?.catch((error: any) => {
            if (error.canceled === true) {
                // means the transient task was canceled due to the "resetChanges"
            }
        });
        return newCreatedTaskContext;
    }

    public updateTaskProperty(taskGuid: string, property: string, value: any): Promise<any> {
        return this.updateTaskProperties(taskGuid, [{ name: property, value }]);
    }

    public async updateTaskProperties(
        taskGuid: string,
        properties: { name: string; value: any }[],
        groupId = "APIGroup",
    ): Promise<any> {
        if (!taskGuid || !properties || properties.length === 0) {
            return Promise.resolve();
        }
        const context = this.taskModel
            .bindContext(`/CBLD_C_TASK_TP(${taskGuid})`, undefined, {
                $$updateGroupId: groupId,
            })
            .getBoundContext();

        await context.requestObject();

        return Promise.all([
            ...properties.map((property) => context.setProperty(property.name, property.value)),
            this.submitTaskChange(groupId),
        ]);
    }

    public sendTagAssignmentChangeRequest(
        taskGuid: string,
        assigningTags: any[],
        unassigningTags: any[],
    ): Promise<any> {
        const contextBinding = this.taskModel.bindContext("/TAG_ASSIGNMENT_CHANGE(...)");
        contextBinding
            .setParameter("TASK_GUID", taskGuid)
            .setParameter("ASSIGNING_TAGS", assigningTags)
            .setParameter("UNASSIGNING_TAGS", unassigningTags);

        return contextBinding.execute("APIGroup");
    }

    public sendEntityRelationsChangeRequest(
        taskGuid: string,
        assigningEntityRelations: any[],
        unassigningEntityRelations: any[],
    ): Promise<any> {
        const contextBinding = this.taskModel.bindContext("/CHANGE_ENTITY_RELATIONS(...)");
        contextBinding
            .setParameter("ENTITY_ID", taskGuid)
            .setParameter("ASSIGNED_RELATIONS", assigningEntityRelations)
            .setParameter("UNASSIGNED_RELATION_IDS", unassigningEntityRelations);

        return contextBinding.execute("APIGroup");
    }

    public sendTaskReferencesChangeRequest(
        taskGuid: string,
        newAssignRefers: any[],
        detachRefers: any[],
    ): Promise<any> {
        const contextBinding = this.taskModel.bindContext("/ATTACHMENT_CHANGE(...)");
        contextBinding
            .setParameter("TASK_GUID", taskGuid)
            .setParameter("ASSIGNING_ATTACHMENTS", newAssignRefers)
            .setParameter("UNASSIGNING_ATTACHMENTS", detachRefers);

        return contextBinding.execute("APIGroup");
    }

    public sendTaskRelationsAssignmentChangeRequest(
        taskGuid: string,
        assigningRelations: any[],
        unassigningRelationIds: any[],
    ): Promise<any> {
        const contextBinding = this.taskModel.bindContext("/CHANGE_RELATIONS_ASSIGNMENT(...)");
        contextBinding
            .setParameter("TASK_GUID", taskGuid)
            .setParameter("ASSIGNED_RELATIONS", assigningRelations)
            .setParameter("UNASSIGNED_RELATION_IDS", unassigningRelationIds);

        return contextBinding.execute("APIGroup");
    }

    public sendUsageRecordRequest(
        entityId: string,
        entityType: string,
        operation?: string,
    ): Promise<any> {
        const contextBinding = this.taskModel.bindContext("/RECORD_ACCESS(...)");
        contextBinding
            .setParameter("ENTITY_ID", entityId)
            .setParameter("ENTITY_TYPE", entityType)
            .setParameter("OPERATION", operation || "READ");
        // Send request directly without the need of "submitBatch"
        return contextBinding.execute("DirectGroup");
    }

    public getTaskCountByReleaseVersion(): Promise<any[]> {
        return this.taskModel
            .bindList("/CBLD_C_TASK_COUNT_BY_RELEASE_VERSION")
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    public initialFeatureServiceModel(): ODataModel {
        this.featureOdataModel ??= this.initODataService(
            "/ui/featuresService/v1/odata/v4/CrossDomainService/",
        );
        return this.featureOdataModel;
    }

    public getFeaturesForAssignDialog(filter: Filter): Promise<any[]> {
        this.initialFeatureServiceModel();
        return this.featureOdataModel!.bindList(
            FEATURES_SERVICE_URL,
            undefined,
            undefined,
            filter,
            {
                $select: "featureId,featureTitle,status,statusName,scopeId,scopeName,lastChangedAt",
                $expand: "entityRelations",
            },
        )
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    private execFucImpForSingleProject(
        projectGuid: string,
        currentTileId: string,
        fucName: string,
    ): Promise<any> {
        const contextBinding = this.taskModel.bindContext(fucName);
        contextBinding.setParameter("PROJECT_GUID", projectGuid);
        contextBinding.setParameter("TILE_ID", currentTileId);

        return contextBinding.execute().then(() => contextBinding.getBoundContext().getObject());
    }

    public sendStakeholdersChangeRequest(
        taskGuid: string,
        assigningStakeholders: any[],
        unassigningStakeholders: any[],
    ): Promise<any> {
        const contextBinding = this.taskModel.bindContext("/CHANGE_STAKEHOLDERS(...)");
        contextBinding
            .setParameter("TASK_GUID", taskGuid)
            .setParameter("ASSIGNED_STAKEHOLDERS", assigningStakeholders)
            .setParameter("UNASSIGNED_STAKEHOLDER_IDS", unassigningStakeholders);
        return contextBinding.execute("APIGroup");
    }

    public initProcessServiceModel(): ODataModel {
        this.processODataModel ??= this.initODataService(
            "/ui/processScopingService-ui/v1/odata/v4/CrossDomainService/",
        );
        return this.processODataModel;
    }

    public initProcessHierarchyServiceModel(): ODataModel {
        this.processHierarchyODataModel ??= this.initODataService(
            "/ui/processHierarchyService-ui/v1/odata/v4/ProcessHierarchyService/",
        );
        return this.processHierarchyODataModel;
    }

    public initLibraryServiceModel(): ODataModel {
        this.libraryODataModel ??= this.initODataService(
            "/ui/library-management-service-ui/v1/odata/v4/lib.CrossDomainService/",
        );
        return this.libraryODataModel;
    }

    public initDocumentServiceModel(): ODataModel {
        this.documentODataModel ??= this.initODataService(
            "/ui/imp-sd-docu-srv/v1/odata/v4/sd.CrossDomainService/",
        );
        return this.documentODataModel;
    }

    public getTemplateTasks(projectGuid: string, typeIds: string[]): Promise<any[]> {
        const filter = new Filter({
            filters: [
                new Filter({
                    path: "ex_project_guid",
                    operator: FilterOperator.EQ,
                    value1: projectGuid,
                }),
                new Filter({
                    path: "is_template",
                    operator: FilterOperator.EQ,
                    value1: true,
                }),
                new Filter({
                    path: "state",
                    operator: FilterOperator.EQ,
                    value1: "ACTIVE",
                }),
                new Filter({
                    filters: typeIds.map(
                        (id) =>
                            new Filter({
                                path: "type_id_v2",
                                operator: FilterOperator.EQ,
                                value1: id,
                            }),
                    ),
                    and: false,
                }),
            ],
            and: true,
        });

        return this.taskModel
            .bindList(TASKS_SERVICE_URL, undefined, new Sorter("short_text"), filter, {
                $expand: `to_texts,to_subtasks($orderby=short_text),to_tag_assignments($orderby=tag_label asc),to_entity_relations($filter=foreign_entity_type eq '${constant.CALM_FOREIGN_TYPE.si}';$expand=to_package)`,
            })
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    public getTaskGuidByUniversalId(universalId: string): Promise<any[]> {
        const filter = new Filter({
            path: "formatted_universal_id",
            operator: FilterOperator.EQ,
            value1: universalId,
        });
        return this.taskModel
            .bindList(TASKS_SERVICE_URL, undefined, undefined, filter, {
                $select: "guid",
            })
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    public getAllQGatesUnderGivenProject(projectGuid: string): Promise<any[]> {
        const queryOption = {
            $select: "guid,short_text",
        };

        const filter = new Filter({
            filters: [
                new Filter({
                    path: "scenario_id",
                    operator: FilterOperator.EQ,
                    value1: "CIP",
                }),
                new Filter({
                    path: "ex_project_guid",
                    operator: FilterOperator.EQ,
                    value1: projectGuid,
                }),
                new Filter({
                    path: "state",
                    operator: FilterOperator.EQ,
                    value1: constant.ITEM_STATE.active,
                }),
                new Filter({
                    path: "type_id_v2",
                    operator: FilterOperator.EQ,
                    value1: constant.TASK_MASTER_TYPE.qualityGate,
                }),
            ],
            and: true,
        });

        return this.taskModel
            .bindList(TASKS_SERVICE_URL, undefined, undefined, filter, queryOption)
            .requestContexts()
            .then(this.getBindingContextObjects);
    }

    public async searchUser(searchText: string): Promise<CBLD_C_USER[]> {
        if (searchText === "") {
            return Promise.resolve([]);
        }

        const filter = new Filter({
            path: "searchText",
            operator: FilterOperator.EQ,
            value1: searchText,
        });
        const bindingContexts = await this.projectModel
            .bindList("/CBLD_C_USER", undefined, undefined, filter)
            .requestContexts();
        return this.getBindingContextObjects(bindingContexts);
    }

    public createServiceRequest(qualityGateKeys: string[]): Promise<any> {
        const contextBinding = this.taskModel.bindContext(
            "/CREATE_SERVICE_REQUESTS(...)",
            undefined,
            {
                $$updateGroupId: "CreateServiceOrderGroup",
            },
        );
        contextBinding.setParameter("QUALITY_GATE_GUIDS", qualityGateKeys);

        return contextBinding.execute();
    }

    public createServiceRequestV2(qualityGateKeys: string[], languageCode: string): Promise<any> {
        const contextBinding = this.taskModel.bindContext(
            "/CREATE_SERVICE_REQUESTS_V2(...)",
            undefined,
            {
                $$updateGroupId: "CreateServiceOrderGroup",
            },
        );
        const payload = {
            QUALITY_GATE_GUIDS: qualityGateKeys,
            LANGUAGE_CODE: languageCode,
        };
        contextBinding.setParameter("SERVICE_REQUEST_PARAMS", payload);

        return contextBinding.execute();
    }

    public generateReqWithAI(payload: any): Promise<any> {
        const contextBinding = this.taskModel.bindContext("/CREATE_REQUIREMENT(...)", undefined, {
            $$updateGroupId: "GenerateRequirementGroup",
        });
        contextBinding.setParameter("CREATE_REQUIREMENT_REQUEST_PARAMS", payload);

        return contextBinding.execute().then(() => contextBinding.getBoundContext().getObject());
    }

    public generateReqWithAIStream(payload: any): Promise<any> {
        // Transform payload from uppercase to lowercase property names for new API
        const transformedPayload = {
            projectId: payload.PROJECT_ID,
            scopeId: payload.SCOPE_ID,
            scopeName: payload.SCOPE_NAME,
            solutionProcessId: payload.SOLUTION_PROCESS_ID,
            contentPackageVersionId: payload.CONTENT_PACKAGE_VERSION_ID,
            documentIds: payload.DOCUMENT_IDS,
            requirementTitle: payload.REQUIREMENT_TITLE,
            templateRequirementId: payload.TEMPLATE_REQUIREMENT_ID,
            enableSapHelpDocumentation: payload.ENABLE_SAP_HELP_DOCUMENTATION
        };

        // Use the new REST API endpoint
        return fetch(AI_CREATE_REQUIREMENT_URL, {
            method: 'POST',
            headers: {
                'x-csrf-token': window.dwc.csrf.csrfToken,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(transformedPayload)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .catch(error => {
            this.errorHandler?.showErrorMessageBox(error.message);
            // throw error;
        });
    }
}
