/* eslint-disable max-params */
/* eslint-disable camelcase */
/* eslint-disable max-len */
sap.ui.define([
    "com/sap/calm/imp/tkm/ui/controller/TaskBaseController",
    "com/sap/calm/imp/tkm/ui/controller/popover/StatusPopoverForListMain",
    "com/sap/calm/imp/tkm/ui/controller/popover/StatusPopoverForListSub",
    "com/sap/calm/imp/tkm/ui/controller/reqGeneration/ProcessSelectionController",
    "com/sap/calm/imp/tkm/ui/controller/reqGeneration/DocumentSelectionController",
    "com/sap/calm/imp/tkm/ui/model/Constant",
    "com/sap/calm/imp/tkm/ui/model/ConstantModels",
    "com/sap/calm/imp/tkm/ui/model/CoreFormatter",
    "com/sap/calm/imp/tkm/ui/model/Sorter",
    "com/sap/calm/imp/tkm/ui/model/project/Project",
    "com/sap/calm/imp/tkm/ui/type/TaskWbsType",
    "com/sap/calm/imp/tkm/ui/type/UnescapedString",
    "com/sap/calm/imp/tkm/ui/util/Util",
    "com/sap/calm/imp/tkm/ui/util/DataUtil",
    "com/sap/calm/imp/tkm/ui/util/DateTimeUtil",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast",
    "sap/ui/core/Fragment",
    "sap/ui/core/Item",
    "sap/m/MessageBox",
    "sap/m/MultiInput",
    "sap/m/Token",
    "com/sap/calm/imp/tkm/ui/handler/ExportHandler",
    "com/sap/calm/imp/tkm/ui/handler/DwcHandler",
    "com/sap/calm/imp/tkm/ui/handler/filter/DynamicFilterOptionHandler",
    "com/sap/calm/imp/tkm/ui/handler/FilterHandler",
    "com/sap/calm/imp/tkm/ui/controller/gantt/GanttChartController",
    "com/sap/calm/imp/tkm/ui/handler/SessionStorageHandler",
    "com/sap/calm/imp/tkm/ui/handler/P13nEngineHandler",
    "com/sap/calm/imp/tkm/ui/handler/filter/FilterBarHandler",
    "sap/m/MultiComboBox",
    "sap/m/Select",
    "sap/m/GroupHeaderListItem",
    "sap/ui/core/library",
    "sap/m/DateRangeSelection",
    "sap/m/SearchField",
    "com/sap/calm/imp/tkm/ui/handler/AcceleratorViewHandler",
    "com/sap/calm/imp/tkm/ui/handler/createTask/CreationOptionHandler",
    "sap/ui/core/SeparatorItem",
    "com/sap/calm/imp/tkm/ui/handler/RequirementHandler",
    "com/sap/calm/imp/tkm/ui/handler/massEdit/MassEditHandler",
    "com/sap/calm/imp/tkm/ui/handler/massEdit/MassDeleteHandler",
    "com/sap/calm/imp/tkm/ui/handler/NavigationHandler",
    "sap/m/Input",
    "sap/m/Text",
    "sap/m/Link",
    "sap/ui/core/InvisibleText"
], /**
 * @param {typeof import('com/sap/calm/imp/tkm/ui/controller/popover/StatusPopoverForListMain').default} MainStatusPopover
 * @param {typeof import('com/sap/calm/imp/tkm/ui/controller/popover/StatusPopoverForListSub').default} SubStatusPopover
 * @param {typeof import('com/sap/calm/imp/tkm/ui/handler/RequirementHandler').default} RequirementHandler
 * @param {typeof import('com/sap/calm/imp/tkm/ui/handler/DwcHandler').default} DwcHandler
 * @param {typeof import('com/sap/calm/imp/tkm/ui/handler/NavigationHandler').default} NavigationHandler
 * @param {typeof import('com/sap/calm/imp/tkm/ui/model/Constant').default} Constant
 * @param {typeof import('com/sap/calm/imp/tkm/ui/model/ConstantModels').default} ConstantModels
 * @param {typeof import('com/sap/calm/imp/tkm/ui/model/project/Project').default} Project
 * @param {typeof import('sap/ui/model/json/JSONModel').default} JSONModel
 * @param {typeof import('sap/m/MessageToast').default} MessageToast
 * @param {typeof import('sap/ui/core/Fragment').default} Fragment
 * @param {typeof import('sap/ui/core/Item').default} Item
 * @param {typeof import('sap/m/MessageBox').default} MessageBox
 * @param {typeof import('sap/m/MultiInput').default} MultiInput
 * @param {typeof import('sap/m/MultiComboBox').default} MultiComboBox
 * @param {typeof import('sap/m/Select').default} Select
 * @param {typeof import('sap/m/GroupHeaderListItem').default} GroupHeaderListItem
 * @param {typeof import('sap/ui/core/library')} CoreLibrary
 * @param {typeof import('sap/m/DateRangeSelection').default} DateRangeSelection
 * @param {typeof import('sap/m/SearchField').default} SearchField
 * @param {typeof import('sap/ui/core/SeparatorItem').default} SeparatorItem
 * @param {typeof import('sap/m/Input').default} Input
 * @param {typeof import('sap/m/Text').default} Text
 * @param {typeof import('sap/m/Link').default} Link
 * @param {typeof import('sap/ui/core/InvisibleText').default} InvisibleText
 */
function (TaskBaseController, MainStatusPopover, SubStatusPopover, ProcessSelectionController, DocumentSelectionController, Constant, ConstantModels, CoreFormatter, Sorter, Project, TaskWbsType, UnescapedString, Util, DataUtil, DateTimeUtil, JSONModel, MessageToast, Fragment, Item, MessageBox, MultiInput, Token, ExportHandler, DwcHandler, DynamicFilterOptionHandler, FilterHandler, GanttChartController, SessionStorageHandler, P13nEngineHandler, FilterBarHandler, MultiComboBox, Select, GroupHeaderListItem, CoreLibrary, DateRangeSelection, SearchField, AcceleratorViewHandler, oCreationOptionHandler, SeparatorItem, RequirementHandler, MassEditHandler, MassDeleteHandler, NavigationHandler, Input, Text, Link, InvisibleText) { // NOSONAR
    "use strict";

    const oConstant = Constant.getInstance();

    const DUMMY_TEAM_GUID = "00000000-0000-0000-0000-000000000000";

    const TASK_FILTER_INFOS = {
        searchFilter: {
            i18nKey: "searchSnappedTitle",
            controlId: "idSearchField"
        },
        projectFilter: {
            i18nKey: "taskFilterByProjectLabel",
            controlId: "projectSelect"
        },
        productFilter: {
            i18nKey: "products",
            controlId: "productComboBox"
        },
        companyFilter: {
            i18nKey: "taskFilterByCompanyLabelNew",
            controlId: "companyComboBox"
        },
        solutionProcessFilter: {
            i18nKey: "taskFilterBySolutionProcessLabel",
            controlId: "solutionProcessComboBox"
        },
        pmHierarchyNodeFilter: {
            i18nKey: "taskFilterByPmHierarchyNodeLabel",
            controlId: "pmHierarchyNodeComboBox"
        },
        timeboxFilter: {
            i18nKey: "taskFilterByTbAndMsLabel",
            controlId: "timeboxFilterMultiInput"
        },
        startDateFilter: {
            i18nKey: "taskFilterByStartDateLabel",
            controlId: "startDateInput"
        },
        dueDateFilter: {
            i18nKey: "taskFilterByPlannedCompletionDateLabel",
            controlId: "dueDateInput"
        },
        statusFilter: {
            i18nKey: "taskFilterByStatusLabel",
            controlId: "statusComboBox"
        },
        subStatusFilter: {
            i18nKey: "taskFilterBySubStatusLabel",
            controlId: "subStatusComboBox"
        },
        defectSubStatusFilter: {
            i18nKey: "taskFilterByDefectSubStatusLabel",
            controlId: "defectSubStatusComboBox"
        },
        qGateSubStatusFilter: {
            i18nKey: "taskFilterByQGateSubStatusLabel",
            controlId: "qGateSubStatusComboBox"
        },
        approvalFilter: {
            i18nKey: "taskFilterByApprovalLabel",
            controlId: "approvalComboBox"
        },
        crewFilter: {
            i18nKey: "taskFilterByCrewLabel",
            controlId: "crewComboBox"
        },
        priorityFilter: {
            i18nKey: "taskFilterByPriorityLabel",
            controlId: "priorityComboBox"
        },
        teamFilter: {
            i18nKey: "taskFilterByTeamLabel",
            controlId: "teamComboBox"
        },
        assigneeFilter: {
            i18nKey: "taskFilterByResponsibleLabel",
            controlId: "assigneeComboBox"
        },
        storyPointsFilter: {
            i18nKey: "taskFilterByStoryPointLabel",
            controlId: "storyPointsComboBox"
        },
        subTasksCompletionFilter: {
            i18nKey: "subItemsCompletion",
            controlId: "subTasksCompletionComboBox"
        },
        effortFilter: {
            i18nKey: "effort",
            controlId: "effortComboBox"
        },
        taskTypeFilter: {
            i18nKey: "taskFilterByTaskTypeLabel",
            controlId: "taskTypeComboBox"
        },
        tagFilter: {
            i18nKey: "taskFilterByTaskTagLabel",
            controlId: "taskTagComboBox"
        },
        taskSourceFilter: {
            i18nKey: "taskFilterByTaskSourceLabel",
            controlId: "taskSourceComboBox"
        },
        releaseVersionFilter: {
            i18nKey: "IMP-PJM-RELEASE-VERSION",
            controlId: "releaseVersionFilterMultiInput"
        },
        taskFavoriteFilter: {
            i18nKey: "favoriteTitle",
            controlId: "taskFavoriteComboBox"
        },
        workstreamFilter: {
            i18nKey: "taskFilterByWorkstreamLabel",
            controlId: "workstreamComboBox"
        },
        deliverableFilter: {
            i18nKey: "taskFilterByDeliverableLabel",
            controlId: "deliverableComboBox"
        },
        stakeholdersFilter: {
            i18nKey: "taskStakeholders",
            controlId: "stakeholdersComboBox"
        },
        createdByFilter: {
            i18nKey: "taskCreatedBy",
            controlId: "createdByComboBox"
        },
        createdOnFilter: {
            i18nKey: "taskCreatedOn",
            controlId: "createdOnDateRangeSelection"
        },
        lastChangedByFilter: {
            i18nKey: "taskChangedBy",
            controlId: "lastChangedByComboBox"
        },
        lastChangedDateFilter: {
            i18nKey: "taskChangedOn",
            controlId: "lastChangedDateRangeSelection"
        },
        taskStateFilter: {
            i18nKey: "taskFilterByTaskStateLabel",
            controlId: "taskStateComboBox"
        },
        taskTemplateFilter: {
            i18nKey: "taskFilterByTaskTemplateLabel",
            controlId: "taskTemplateComboBox"
        },
        userTypeFilter: {
            i18nKey: "taskUserTypeLabel",
            controlId: "userTypeComboBox"
        },
        testPlanFilter: {
            i18nKey: "testPlanLabel",
            controlId: "testPlanComboBox"
        },
        externalReferenceFilter: {
            i18nKey: "externalReferenceLabel",
            controlId: "externalReferenceComboBox"
        }
    };

    const MAX_SHOW_SELECTED_FILTERS_NUMBER = 5;

    const MASS_EDIT_KEEP_EXISTING_VALUE = {
        id: "keep",
        i18nKey: "keepValuePlaceholder"
    };

    const VARIANT_LOADING_CONTEXT = {
        init: "INIT", // initial loading
        switch: undefined, // switch project
        cancel: "CANCEL" // cancel adapt filter dialog visibility change
    };

    const ICON_ID_TO_POPOVER = new Map();

    const DISPLAY_INFO_BAR_FOR_FILTER = "/displayInfoBarForFilter";
    const DISPLAY_INFO_BAR_FOR_SELECTION = "/displayInfoBarForSelection";

    const HEADER_EXPANDED = "/headerExpanded";
    const SEARCH_FIELD = "/searchField";
    const SHOULD_SELECT_GROWING_ITEMS = "/shouldSelectGrowingItems";
    const TABLE_DESELECTED_ITEM_PATH = "/tableDeselectedItemPath";
    const TABLE_TOTAL_COUNT = "/tableTotalCount";

    const MASS_EDIT_BTN_ENABLE = "/massEditBtnEnable";
    const MASS_DELETE_BTN_ENABLE = "/massDeleteButtonEnable";
    const ENABLE_CLEAR_BTN = "/enableClearBtn";
    const WORKSTEAMS = "/workstreams";
    const SELECTED_PROJECT_GUID = "/selectedProject/guid";
    const CURRENT_DISPLAYED_TAB = "/currentDisplayedTab";
    const DISPLAYED_VIEW = "/displayedView";

    return TaskBaseController.extend("com.sap.calm.imp.tkm.ui.controller.TaskList", {
        TaskWbsType: TaskWbsType,

        onInit: async function () {
            TaskBaseController.prototype.onInit.apply(this, arguments);
            const oListDynamicPage = this.byId("idTaskListPage");
            this.oScrollDelegate = oListDynamicPage.getScrollDelegate();
            this.oTaskTable = this.byId("taskTable");

            // if somehow for unknown reason the page busy is not finished, we end it after 5 seconds
            setTimeout(() => {
                this.endBusyInPage("beginColumnPages");
            }, 5 * 1000);

            this.setModel(new JSONModel({
                searchField: "",
                snappedTitle: this.oBundle.getText("filteredContentNone"),
                tableTitle: this.oBundle.getText("taskItems"),
                shouldSelectGrowingItems: false,
                massEditBtnEnable: false,
                tableDeselectedItemPath: [],
                exportBtnEnabled: true,
                pageBusy: true, // because of early route dispatching, task list page will be opened much earlier than the request is triggered
                busy: false,
                noDataText: this.oBundle.getText("noItemText"),
                headerExpanded: true,
                manualUploadFileBtnEnabled: false,
                massDeleteButtonEnable: false,
                displayInfoBarForFilter: false,
                displayInfoBarForSelection: false,
                currentDisplayedTab: oConstant.MASTER_VIEW_TAB.taskList,
                displayedView: oConstant.LIST_VIEW_TYPE.taskTable
            }), oConstant.JSON_MODELS.viewModel);

            this.setModel(new JSONModel({}), oConstant.JSON_MODELS.dateFilterModel);
            // make sure the route matched event is registered synchronously
            this.oRouter.getRoute(oConstant.ROUTE_TASK_LIST).attachPatternMatched(this._onRouteMatched, this);

            // initialize the filter bar earlier to make sure filter UI stable during loading
            this.initializeFilterBar();
            this.initializeSaveAsTileAction();

            await this.oComponent.calmContextReadyPromise;
            this.buildProjectModel(this.oCalmContextModel.getProperty("/selectedProject"));

            this.oEventBus.subscribe(oConstant.EVENT_CHANNEL, "taskManualCreated", this.refreshTaskTable, this);
            this.oEventBus.subscribe(oConstant.EVENT_CHANNEL, "taskDeleted", this.postTaskDeleted, this);
            this.oEventBus.subscribe(oConstant.EVENT_CHANNEL, "taskUpdated", this.refreshTaskTable, this);
            this.oEventBus.subscribe(oConstant.EVENT_CHANNEL, "loadTaskListPageData", this.loadTaskListPageData, this);
            this.oEventBus.subscribe(oConstant.EVENT_CHANNEL, "navigateAndScroll", this.navigateAndScroll, this);
            this.oEventBus.subscribe(oConstant.EVENT_CHANNEL, "commentCountUpdated", this.updateCommentCount, this);
            this.oEventBus.subscribe(oConstant.EVENT_CHANNEL, "taskDetailWithDifferentProject", this.switchProject, this);
            this.oEventBus.subscribe(oConstant.EVENT_CHANNEL, "taskFavoriteChanged", this.refreshFilteredFavoriteTasks, this);
            this.oEventBus.subscribe(oConstant.EVENT_CHANNEL, "refreshTags", this.refreshTags, this);
            this.oEventBus.subscribe(oConstant.EVENT_CHANNEL, "restoreACCFocus", this.setACCFocus, this);
            this.oEventBus.subscribe(oConstant.EVENT_CHANNEL, "documentSelectionUpdated", this.onDocumentSelectionUpdated, this);

            this.isMassEditOkSave = true;

            this.oSessionStorageHandler = new SessionStorageHandler(`CALM_PTM_${this.oComponent.tileInfo.sessionStorageKey}_${this.oCurrentUserInfo.userId}`, this.oFilterBar);
            this.oP13nEngineHandler = new P13nEngineHandler(this.oComponent.tileInfo, this, this.oBundle);
            this.oFilterBarHandler = new FilterBarHandler(this.oComponent, this.oFilterBar, this.oSessionStorageHandler, this.oP13nEngineHandler);

            if (this.oCalmContextModel.getProperty("/tileInfo/id") !== oConstant.TILE_ID.default) {
                this.byId("iconTabBar").addStyleClass("iconTabBarHeaderVisibility");
                oListDynamicPage.setStickySubheaderProvider();
            }
        },

        onExit: function () {
            TaskBaseController.prototype.onExit.apply(this, arguments);

            this.oP13nEngineHandler.onExit();
            this.oMassEditHandler?.onExit();

            this.oEventBus.unsubscribe(oConstant.EVENT_CHANNEL, "taskManualCreated", this.refreshTaskTable, this);
            this.oEventBus.unsubscribe(oConstant.EVENT_CHANNEL, "taskDeleted", this.postTaskDeleted, this);
            this.oEventBus.unsubscribe(oConstant.EVENT_CHANNEL, "taskUpdated", this.refreshTaskTable, this);
            this.oEventBus.unsubscribe(oConstant.EVENT_CHANNEL, "loadTaskListPageData", this.loadTaskListPageData, this);
            this.oEventBus.unsubscribe(oConstant.EVENT_CHANNEL, "navigateAndScroll", this.navigateAndScroll, this);
            this.oEventBus.unsubscribe(oConstant.EVENT_CHANNEL, "commentCountUpdated", this.updateCommentCount, this);
            this.oEventBus.unsubscribe(oConstant.EVENT_CHANNEL, "taskDetailWithDifferentProject", this.switchProject, this);
            this.oEventBus.unsubscribe(oConstant.EVENT_CHANNEL, "taskFavoriteChanged", this.refreshFilteredFavoriteTasks, this);
            this.oEventBus.unsubscribe(oConstant.EVENT_CHANNEL, "refreshTags", this.refreshTags, this);
            this.oEventBus.unsubscribe(oConstant.EVENT_CHANNEL, "restoreACCFocus", this.setACCFocus, this);
            this.oEventBus.unsubscribe(oConstant.EVENT_CHANNEL, "documentSelectionUpdated", this.onDocumentSelectionUpdated, this);
        },

        _onRouteMatched: async function (oEvent) {
            await this.oComponent.calmContextReadyPromise;

            const oMatchedRoute = this.oRouter.getRouteInfoByHash(this.oRouter.getHashChanger().getHash());
            if (!oMatchedRoute || oMatchedRoute.name !== "default") {
                return Promise.resolve();
            }

            this.oEventBus.publish(this.oComponent.EVENT_CHANNEL, "closePage", {
                bCloseAllPages: true
            });

            this.clearTableSelection();

            if (this.bForceSkipLoading || this.oComponent.listPageLoaded) {
                return Promise.resolve();
            }
            const oArguments = oEvent.getParameter("arguments");
            return this.oVariantLoadedPromise.then(() => this.loadTaskListPageData(undefined, undefined, oArguments));
        },

        setACCFocus: function (sChannel, sEvent, oParams) {
            if (oParams.sFocusElementRoute !== oConstant.ROUTE_TASK_LIST) {
                return;
            }
            if (oParams?.sFocusElementId) {
                this.oAccHandler.setFocus(this.byId(oParams?.sFocusElementId));
            } else {
                const aTaskItems = this.oTaskTable.getItems();
                if (aTaskItems.length > this.navigatedRowIndex) {
                    this.oAccHandler.setFocus(aTaskItems[this.navigatedRowIndex]);
                }
            }
        },

        /**
         * @param {object} [oParams] Optional parameters
         */
        loadTaskListPageData: function (sChannel, sEvent, oParams) {
            // directly open task detail page won't trigger route matched event, so we cancel page busy here
            const viewModel = this.getViewModel();
            viewModel.setProperty("/pageBusy", false);

            const sViewType = viewModel.getProperty(DISPLAYED_VIEW);
            if (oParams?.viewType === oConstant.LIST_VIEW_TYPE.ganttChart
                || sViewType === oConstant.LIST_VIEW_TYPE.ganttChart) {
                // initial loading with gantt view
                this.loadTaskGanttPageData();
            } else if (sViewType === oConstant.LIST_VIEW_TYPE.taskTable) {
                // initial loading with table view
                this.loadTaskTablePageData();
            } else if (sViewType === oConstant.LIST_VIEW_TYPE.kanban) {
                // initial loading with kanban view
                this.loadTaskKanbanPageData();
            }
        },

        openTableSettingDialog: function (oEvent, isGroup) {
            this.oP13nEngineHandler.openTableSettingDialog(oEvent, isGroup);
        },

        refreshFilteredFavoriteTasks: function () {
            if (this.projectRelatedInfo.taskFavoriteFilter.selectedIds.length === 1) {
                this.oP13nEngineHandler.applyState(this.projectRelatedInfo.tableColumnSetting, this.projectRelatedInfo.sorting);
            }
        },

        switchProject: function (sChannel, sEvent, oParams) {
            if (oParams && oParams.sTargetProjectGuid !== undefined) {
                // To reset the flag and reload task list with startup parameter
                this.oComponent.listPageLoaded = false;
                this.fireProjectSelectionChange(oParams.sTargetProjectGuid, true);
            }
        },

        updateCommentCount: function (sChannel, sEvent, oParams) {
            if (!oParams || !oParams.path || !Util.isValidNumber(oParams.deltaCount)) {
                return false;
            }
            const oMatchedItem = this.oTaskTable.getItems().find((item) => item.getBindingContext()?.getProperty("guid") === oParams.path);
            if (!oMatchedItem) {
                return false;
            }

            const oTaskContext = oMatchedItem.getBindingContext();
            const iNewCount = oTaskContext.getProperty("comment_count") + oParams.deltaCount;
            oTaskContext.setProperty("comment_count", iNewCount, null);
            return true;
        },

        initializeSaveAsTileAction: function () {
            this.byId("saveTileAction").setBeforePressHandler(this.onBeforeOpenSaveTileDialog.bind(this));
        },

        initializeFilterBar: function () {
            this.oFilterBar = this.byId("filterBar");
            const sStandardVariantText = this.oBundle.getText(this.oComponent.tileInfo.variantStandardItemTextKey);
            this.oFilterBar.setPersistencyKey(this.oComponent.tileInfo.variantStorageKey);
            this.oFilterBar.getVariantManagement().setStandardItemText(sStandardVariantText);

            this.oFilterBar.customInitializeVariantManagement(this);
            this.oFilterBar.attachInitialized(this.onFilterBarInitialized, this);
            this.oVariantLoadedPromise = new Promise((resolve) => {
                this.resolveVariantLoadedPromise = resolve;
            });

            const oSettings = {
                id: this.getView().createId("idSearchField"),
                search: this.onTaskListSearchFieldChange.bind(this),
                value: { path: "viewModel>/searchField" },
                maxLength: 100,
                placeholder: this.oBundle.getText("tasklistSearchFieldPlaceholder"),
                showSearchButton: true
            };

            this.oFilterBar.setBasicSearch(new SearchField(oSettings));
            this.oControlFilterMap = this._generateControlFilterMap();
        },

        onDragDrop: async function (oEvent) {
            if (!this.oCalmContextModel.getProperty("/authTask/hasEditAuth")) {
                MessageBox.error(this.oBundle.getText("noAuthWithKanBanItemError"));
                return;
            }

            const oDraggedTaskCard = oEvent.getParameter("draggedControl");
            const oCurrentTask = oDraggedTaskCard.getBindingContext(oConstant.JSON_MODELS.kanbanTasks).getObject();
            if (this.isInvalidDragOperation(oCurrentTask)) {
                MessageBox.error(this.oBundle.getText(oCurrentTask.state === oConstant.ITEM_STATE.obsolete ? "dragObsoleteKanbanItemErrorText" : "dragCBCKanbanItemErrorText"));
                return;
            }

            const bIsDef = oCurrentTask.type_id_v2 === oConstant.TASK_MASTER_TYPE.defectV2;
            const sTargetStatusKey = this.getTargetDroppedStatusKey(oEvent.getParameter("droppedControl").data("statusKey"), oCurrentTask.type_id_v2, bIsDef);
            if (oCurrentTask.status_id_v2 === sTargetStatusKey || oCurrentTask.sub_status_id === sTargetStatusKey) {
                return;
            }

            this.updateKanbanItem(oCurrentTask.guid, bIsDef ? "sub_status_id" : "status_id_v2", sTargetStatusKey);
            if (bIsDef) {
                oCurrentTask.sub_status_id = sTargetStatusKey;
            } else {
                oCurrentTask.status_id_v2 = sTargetStatusKey;
            }
            this.getModel(oConstant.JSON_MODELS.kanbanTasks).updateBindings(true);
        },

        /**
         * @param {import('sap/ui/core/dnd/DropInfo').DropInfo$DropEvent} oEvent
         * @returns
         */
        onRequirementDrop: function (oEvent) {
            if (!this.oCalmContextModel.getProperty("/authTask/hasEditAuth")) {
                MessageBox.error(this.oBundle.getText("noAuthWithKanBanItemError"));
                return;
            }

            const requirement = oEvent
                .getParameter("draggedControl")
                ?.getBindingContext(oConstant.JSON_MODELS.kanbanTasks)
                ?.getObject();
            const sTargetStatusKey = oEvent.getParameter("droppedControl")?.data("statusKey");

            if (requirement.sub_status_id === sTargetStatusKey) {
                return;
            }

            if (requirement.state === oConstant.ITEM_STATE.obsolete) {
                MessageBox.error(this.oBundle.getText("dragObsoleteKanbanItemErrorText"));
                return;
            }

            if (requirement.approval_state_id === oConstant.REQUIREMENT_APPROVAL_STATE.readyForApproval) {
                MessageBox.error(this.oBundle.getText("reqNonDraggable"));
                return;
            }

            const validSubStatusIds = RequirementHandler.getSubStatusListByApprovalState(requirement.approval_state_id).map((s) => s.id);
            if (!validSubStatusIds.includes(sTargetStatusKey)) {
                MessageBox.error(this.oBundle.getText("dragReqKanbanItemErrorText"));
                return;
            }

            this.updateKanbanItem(requirement.guid, "sub_status_id", sTargetStatusKey);

            requirement.sub_status_id = sTargetStatusKey;
            this.getModel(oConstant.JSON_MODELS.kanbanTasks).updateBindings(true);
        },

        updateKanbanItem: function (sItemGUID, sUpdateProperty, sNewValue) {
            const oViewModel = this.getModel(oConstant.JSON_MODELS.viewModel);
            oViewModel.setProperty("/busy", true);

            return this.oDataHandler.updateKanbanItem(sItemGUID, sUpdateProperty, sNewValue).then(() => {
                this._showUpdateToastForStatus(sNewValue);
                this.oEventBus.publish(this.oComponent.EVENT_CHANNEL, "taskUpdated");
                this.resetItemLoadedFlag();
            }).catch(this.handleTaskOperationError.bind(this)).finally(oViewModel.setProperty("/busy", false));
        },

        _showUpdateToastForStatus: function (sNewStatusId) {
            return this.isNewStatusInCurFilter(sNewStatusId)
                ? MessageToast.show(this.oBundle.getText("taskSaveSuccessful"))
                : MessageToast.show(this.oBundle.getText("taskSaveSuccessfulWithNewValueNotInFilter"), { duration: 5000, width: "25rem" });
        },

        isInvalidDragOperation: function (oCurrentTask) {
            return oCurrentTask.source_v2 === oConstant.TASK_MASTER_SOURCE.centralBusinessConfig || oCurrentTask.state === oConstant.ITEM_STATE.obsolete;
        },

        getTargetDroppedStatusKey: function (sDroppedListStatusKey, sCurrentTaskType, bIsReqOrDef) {
            if (bIsReqOrDef) {
                return sDroppedListStatusKey;
            }
            let sTargetStatusKey;
            const iStatusIndex = sCurrentTaskType === oConstant.TASK_MASTER_TYPE.userStoryV2 ? 1 : 0;
            switch (sDroppedListStatusKey) {
                case "OPEN":
                    sTargetStatusKey = oConstant.STATUS_IDS_OF_OPEN[iStatusIndex]; break;
                case "INPROGRESS":
                    sTargetStatusKey = oConstant.STATUS_IDS_OF_IN_PROCESS[iStatusIndex]; break;
                case "BLOCKED":
                    sTargetStatusKey = oConstant.STATUS_IDS_OF_BLOCK[iStatusIndex]; break;
                case "CLOSE":
                    sTargetStatusKey = oConstant.STATUS_IDS_OF_DONE[iStatusIndex]; break;
                case "NOTRELEVANT":
                    sTargetStatusKey = oConstant.STATUS_IDS_OF_NO[iStatusIndex]; break;
            }

            return sTargetStatusKey;
        },

        onKanbanOpen: async function () {
            const oViewModel = this.getViewModel();
            oViewModel.setProperty("/busy", true);
            oViewModel.setProperty(DISPLAYED_VIEW, oConstant.LIST_VIEW_TYPE.kanban);
            if (!this.oKanbanFragment) {
                Fragment.load({
                    id: "fIdKanban",
                    name: `com.sap.calm.imp.tkm.ui.fragment.kanban.Kanban${this.oCalmContextModel.getProperty("/tileInfo/id")}`,
                    controller: this
                }).then((oKanban) => {
                    this.oView.addDependent(oKanban);
                    this.oKanbanFragment = oKanban;
                    this.byId("tabItemList").addContent(oKanban);
                });
            }
            this._initTaskCreationOptions(true);
            this.loadKanbanData();
        },

        onTaskViewChanged: function (oEvent) {
            const sListViewType = oEvent.getSource().getSelectedKey();
            switch (sListViewType) {
                case oConstant.LIST_VIEW_TYPE.taskTable:
                    this.onTaskTableOpen();
                    break;
                case oConstant.LIST_VIEW_TYPE.ganttChart:
                    this.onGanttChartOpen();
                    break;
                case oConstant.LIST_VIEW_TYPE.kanban:
                    this.onKanbanOpen();
            }
            this.oSessionStorageHandler.updateListViewType(sListViewType);
            this.updateVariantStatus();
        },

        onKanBanTaskNavPress: function (oEvent) {
            this.oClickedKanbanCard = oEvent.getSource();
            const selectedItemId = this.oClickedKanbanCard.getBindingContext(oConstant.JSON_MODELS.kanbanTasks).getProperty("formatted_universal_id");
            const oFCLModel = this.oComponent.getModel(oConstant.JSON_MODELS.fclStatus);
            const bReplace = oFCLModel.getProperty("/isThreeColumn") || oFCLModel.getProperty("/isTwoColumn") && this.isPageNotInHistory();
            this.oRouter.navTo(oConstant.ROUTE_TASK_DETAIL, {
                objectId: selectedItemId
            }, bReplace);
        },

        onNavToTaskTrend: function () {
            Util.crossAppNavigate(oConstant.TASK_TREND_NAV_PARA);
        },

        onNavToTaskBurnUp: function () {
            Util.crossAppNavigate(oConstant.TASK_BURNUP_NAV_PARA);
        },

        onBeforeOpenSaveTileDialog: function () {
            const oVariantManagement = this.oFilterBar.getVariantManagement();
            const sCurrentVariantId = oVariantManagement.getCurrentVariantId();
            const bIsStandardVariant = sCurrentVariantId === "";
            const sCurrentVariantName = bIsStandardVariant ? oVariantManagement.getStandardItemText() : oVariantManagement.getItemByKey(sCurrentVariantId).getText();
            const oCrossNavigationParam = {
                target: this.oCalmContextModel.getProperty("/tileInfo/navTarget"),
                params: {
                    variantId: bIsStandardVariant ? oVariantManagement.getStandardVariantKey() : sCurrentVariantId
                }
            };
            const sHash = Util.getNavigationHash(oCrossNavigationParam);
            const oBookMarkButton = this.byId("saveTileAction");
            oBookMarkButton.setTitle(sCurrentVariantName);
            oBookMarkButton.setTileIcon(oConstant.DEFAULT_TILE_ICON);
            oBookMarkButton.setCustomUrl(sHash);
        },

        onNavToRequirementTrace: function () {
            Util.crossAppNavigate(oConstant.REQUIREMENT_TRACE_NAV_PARA);
        },

        onNavToDefectReporting: function () {
            Util.crossAppNavigate(oConstant.DEFECT_REPORTING_NAV_PARA);
        },

        fireProjectSelectionChange: async function (sTargetProjectKey, bProjectMismatchBetweenListAndDetail) {
            this.oComponent.oProjectSwitchedPromise = this.onProjectSelectionChanged(undefined, sTargetProjectKey, bProjectMismatchBetweenListAndDetail);
            await this.oComponent.calmContextReadyPromise;
            this.setArchivedProjectAsTemporarySelectedItem();
        },

        setArchivedProjectAsTemporarySelectedItem: function () {
            // Make sure the project info ready with 'selectedProject' of CalmContextModel
            const oProjectSelect = this.byId("projectSelect");
            const sArchivedProjectGuid = this.oCalmContextModel.getProperty("/selectedProject/guid");
            const sTargetProjectName = this.oUnescapedString.formatValue(this.oCalmContextModel.getProperty("/selectedProject/name"));

            setTimeout(() => {
                oProjectSelect.setSelectedItem(new Item({
                    key: sArchivedProjectGuid,
                    text: sTargetProjectName
                }));
            }, 0);
        },

        onFilterDialogClosed: function () {
            this.oSessionStorageHandler.setSessionStorage();
        },

        onAfterVariantLoad: async function (oEvent) { // NOSONAR
            await this.oComponent.calmContextReadyPromise;
            const sAction = oEvent.getParameter("context");
            const oCurrentProjectGuid = this.oCalmContextModel.getProperty(SELECTED_PROJECT_GUID);

            if (sAction === VARIANT_LOADING_CONTEXT.cancel) {
                return;
            }
            // initial loading scenarios
            if (sAction === VARIANT_LOADING_CONTEXT.init) {
                // only custom default variant (default standard variant won't trigger this event)
                if (Util.containsStartupParameters(this.oComponent.oStartupParams)) {
                    // navigation parameters have highest priority, do nothing
                    this.getViewModel().setProperty(DISPLAYED_VIEW, oConstant.LIST_VIEW_TYPE.taskTable);
                    return;
                }
                const oVariantContent = this.oFilterBarHandler.getCurrentVariantContent();
                // apply list view type from variant to view model on initial load
                const oSessionStorageContent = this.oSessionStorageHandler.getFiltersAndSettingsFromSessionStorage();
                const sTaskListViewType = oSessionStorageContent?.taskListViewType
                    || oVariantContent.taskListViewType
                    || oConstant.LIST_VIEW_TYPE.taskTable;
                this.getViewModel().setProperty(DISPLAYED_VIEW, sTaskListViewType);
                if (oVariantContent.projectFilter[0] === oCurrentProjectGuid) {
                    // variant project same with project in global context
                    return;
                }

                // session storage has higher priority than custom default variant
                const projectIdFromSession = oSessionStorageContent?.projectFilter && oSessionStorageContent.projectFilter[0];
                if (projectIdFromSession && oVariantContent.projectFilter[0] !== projectIdFromSession) {
                    return;
                }

                // update variant project to global context and trigger project filter change
                const sTargetProjectGuid = DwcHandler.setSelectedProject(oVariantContent.projectFilter[0]) ? oVariantContent.projectFilter[0] : oCurrentProjectGuid;
                this.bForceSkipLoading = true; // avoid unnecessary data request with router match handler
                this.resolveVariantLoadedPromise(); // make route match handler proceed
                this.fireProjectSelectionChange(sTargetProjectGuid);
                return;
            }

            // switch scenarios
            this._ignoreGuidFilterAndCloseInfoBar();

            // switch to standard variant: apply default filters (global context + active)
            const oVariantContent = this.oFilterBarHandler.getCurrentVariantContent();
            if (oVariantContent.isStandardVariant) {
                const sProjectGuidOfCalmContext = DwcHandler.getSelectedProjectGuid();
                if (oCurrentProjectGuid === sProjectGuidOfCalmContext) {
                    this.updateChartFilterModelByVariant(oVariantContent);
                    this.onFilterSelectionChanged();
                } else {
                    // For hidden project with variant
                    this.fireProjectSelectionChange(sProjectGuidOfCalmContext);
                }
                return;
            }

            // switch to custom variant
            // update task list view type from custom variant
            this.getViewModel().setProperty(DISPLAYED_VIEW, oVariantContent.taskListViewType || oConstant.LIST_VIEW_TYPE.taskTable);
            if (oVariantContent.projectFilter[0] === oCurrentProjectGuid) {
                // variant project same with global context: apply variant filters
                this.updateSelectedScopes(oVariantContent.companyFilter);
                this.updateChartFilterModelByVariant(oVariantContent);
                this.onFilterSelectionChanged();
            } else {
                // variant project different with global context: switch project
                DwcHandler.setSelectedProject(oVariantContent.projectFilter[0]);
                this.fireProjectSelectionChange(oVariantContent.projectFilter[0]);
            }
        },

        updateChartFilterModelByVariant: function (oVariantContent) { // NOSONAR
            let aVariantKeys = [];
            // Reset data
            this.projectRelatedInfo.createdOnFilter = [];
            this.projectRelatedInfo.lastChangedDateFilter = [];
            this.projectRelatedInfo.sorting = {};
            this.projectRelatedInfo.tableColumnSetting = undefined;

            Object.keys(oVariantContent).forEach((property) => {
                if (property === "sorting") {
                    this.projectRelatedInfo.sorting = oVariantContent.sorting;
                    return;
                }
                if (property === "tableColumnSetting") {
                    this.projectRelatedInfo.tableColumnSetting = oVariantContent.tableColumnSetting;
                    return;
                }

                const aFilterKeys = this.projectRelatedInfo[property];
                if (!Array.isArray(aFilterKeys)) {
                    return;
                }
                if (property === "searchFilter") {
                    this.projectRelatedInfo[property] = oVariantContent[property];
                    return;
                }
                if ((property === "lastChangedDateFilter" || property === "createdOnFilter") && oVariantContent[property].length > 0) {
                    if (Number.isInteger(oVariantContent[property][0].startDate) && Number.isInteger(oVariantContent[property][0].endDate)) {
                        this.projectRelatedInfo[property] = oVariantContent[property];
                    }
                    return;
                }
                if (property === "timeboxFilter" || property === "releaseVersionFilter") {
                    aVariantKeys = oVariantContent[property].map((item) => item.id);
                } else {
                    aVariantKeys = oVariantContent[property];
                }

                aFilterKeys.forEach((item) => {
                    item.selected = aVariantKeys.some((selectedItemKey) => selectedItemKey === item.id);
                });
            });

            // make items selected according to variant content
            this._supplementSelectionInfoToChartFilterModel(oVariantContent);
        },

        onFilterBarInitialized: function (oEvent) {
            const oVariantManagement = oEvent.getSource().getVariantManagement();

            this.byId("dynamicPageHeading").setHeading(oVariantManagement);

            if (Util.containsStartupParameters(this.oComponent.oStartupParams)) {
                if (this.oComponent.oStartupParams.variantId) {
                    // if startup parameter specify one variant key, then with highest priority
                    oVariantManagement.setCurrentVariantId(this.oComponent.oStartupParams.variantId, false);
                } else {
                    // navigation scenario: set standard variant selected with modified flag
                    oVariantManagement.clearVariantSelection();
                    oVariantManagement.currentVariantSetModified(true);
                }
            }

            this.resolveVariantLoadedPromise();
        },

        onTabSelect: function (oEvent) {
            this.getViewModel().setProperty(CURRENT_DISPLAYED_TAB, oEvent.getParameter("selectedKey"));
            this._adjustStyle();
            this.refreshTaskTable();
        },

        _adjustStyle: function () {
            if (this.isGanttChartDisplayed()) {
                // gantt chart needs to occupy exactly the viewpoint so the position should be "absolute"
                this.byId("iconTabBar").removeStyleClass("content_position");
            } else {
                // task list and accelerator view is scrollable so the position should be "relative" letting container fits the content
                this.byId("iconTabBar").addStyleClass("content_position");
            }
        },

        _generateControlFilterMap: function () {
            const oControlFilterMap = new Map();
            for (const [filterName, filterInfo] of Object.entries(TASK_FILTER_INFOS)) {
                const oFilterControl = this.byId(filterInfo.controlId);
                oControlFilterMap.set(filterName, oFilterControl);
                oControlFilterMap.set(oFilterControl, filterName);
            }
            return oControlFilterMap;
        },

        getSourceGroupHeader: function (oGroup) {
            const groupI18nKey = Object.values(oConstant.TASK_SOURCES).find((source) => source.group.category === oGroup.key)?.group.groupI18n;
            return groupI18nKey ? new SeparatorItem({ text: this.oBundle.getText(groupI18nKey) }) : null;
        },

        /**
         * @param {object} [oFilters] Parameter object to control filter construction with regards to variant, start-up parameter and session storage.
         */
        _loadTaskTableData: async function (oFilters) {
            if (this.oComponent.oRelationIdParsePromise && !this.oComponent.listPageLoaded) {
                // make sure relId has been translated to task guid and then load task list table
                await this.oComponent.oRelationIdParsePromise;
            }
            this._constructChartFilterModel(oFilters);
            this._supplementSelectionInfoToChartFilterModel(oFilters);
            // when project switching, we need to ignore variant, also means we need to clear searchField and not apply table setting
            // for other scenarios, search text not changed but apply table setting
            this.onFilterSelectionChanged();
            this.updateAppliedFiltersCount();
        },

        loadTaskTablePageData: async function () {
            const oFilters = this.oFilterBarHandler.getInitialFilters();
            this._loadTaskTableData(oFilters);
        },

        loadTaskGanttPageData: async function () {
            // loading task list page with gantt view
            const oFilters = this.oFilterBarHandler.getInitialFilters();
            this._constructChartFilterModel(oFilters);
            this._supplementSelectionInfoToChartFilterModel(oFilters);
            this._updateChartFilterModel();
            this.onGanttChartOpen();
            this._updatePageSnappedTitle();
        },

        loadTaskKanbanPageData: function () {
            const oFilters = this.oFilterBarHandler.getInitialFilters();
            this._constructChartFilterModel(oFilters);
            this._supplementSelectionInfoToChartFilterModel(oFilters);
            this._updateChartFilterModel();
            this.onKanbanOpen();
            this._updatePageSnappedTitle();
        },

        removeNavigatedTaskFilter: function () {
            // after close info bar, should never apply guid filter again, set a permanent flag since guid filter will not disappear from URL
            this.bPermanentlyIgnoreGuidFilter = true;
            this._ignoreGuidFilterAndCloseInfoBar();
            this.restoreDefaultFilters();
            this.onFilterSelectionChanged();
        },

        _ignoreGuidFilterAndCloseInfoBar: function () {
            this.bIgnoreGuidFilter = true;
            this.getViewModel().setProperty(DISPLAY_INFO_BAR_FOR_FILTER, false);
        },

        restoreDefaultFilters: function () {
            const oDefaultFiltersByTile = this.oFilterBarHandler.getInitialFiltersIgnoreAll();
            this._supplementSelectionInfoToChartFilterModel(oDefaultFiltersByTile);
            this._updateChartFilterModel();
            this.updateAppliedFiltersCount();
            this._updatePageSnappedTitle();
        },

        isSkipRefresh4UpdateStatus: function (oParams) {
            // avoid refresh task list table that will hard to found the updated item especially for item not within the first 20 items
            return oConstant.STATUS_RELATED_PROPERTIES.includes(oParams?.updatedProperty) && this.isNewStatusInCurFilter(oParams?.updatedNewValue) && !this.isStatusInSortOrGroup();
        },

        refreshTaskTable: function (sChannel, sEvent, oParams) {
            if (this.isSkipRefresh4UpdateStatus(oParams)) {
                return;
            }

            this._refreshTaskTableIfNecessary(true);

            // refresh Gantt Chart if displayed
            this._refreshGanttChartIfNecessary();

            this._refreshKanbanIfNecessary();

            // refresh Accelerator View if displayed
            this._refreshAcceleratorViewIfNecessary();
        },

        _refreshTaskTableIfNecessary: function (bOnlyRefresh) {
            if (this.isTaskTableDisplayed()) {
                this.oP13nEngineHandler.applyState(this.projectRelatedInfo.tableColumnSetting, this.projectRelatedInfo.sorting, bOnlyRefresh);
            }
        },

        _refreshGanttChartIfNecessary: function () {
            if (this.isGanttChartDisplayed()) {
                this.loadGanttChartData();
            }
        },

        _refreshKanbanIfNecessary: async function () {
            if (this.isKanbanDisplayed()) {
                this.loadKanbanData();
            }
        },

        _refreshAcceleratorViewIfNecessary: function () {
            if (this.isAcceleratorViewDisplayed()) {
                this.loadAcceleratorViewData();
            }
        },

        refreshTags: function () {
            return this.oDataHandler.getTags().then((aTags) => {
                this.oCalmContextModel.setProperty("/tags", aTags);
                const aPreviousSelectedIds = this.projectRelatedInfo.tagFilter.selectedIds;
                this.projectRelatedInfo.tagFilter = aTags.map((oItem) => ({
                    id: oItem.ID,
                    text: oItem.label
                }));
                this.projectRelatedInfo.tagFilter.selectedIds = aPreviousSelectedIds;
                this.getModel(oConstant.JSON_MODELS.chartFilter).updateBindings();
            });
        },

        updateAppliedFiltersCount: function () {
            if (this.oFilterBar._updateToolbarText) {
                // update count unmber of applied filter
                this.oFilterBar._updateToolbarText();
            }
        },

        _supplementSelectionInfoToChartFilterModel: function (sVariantFilters) {
            const variantFilters = JSON.parse(JSON.stringify(sVariantFilters));

            Object.keys(TASK_FILTER_INFOS).forEach(function (filterName) {
                let aSelectedKeys = [];
                const oFilterControl = this.oControlFilterMap.get(filterName);
                if (filterName === "timeboxFilter" && Array.isArray(variantFilters.timeboxFilter)) {
                    aSelectedKeys = variantFilters.timeboxFilter.map((item) => item.id);
                } else if (filterName === "releaseVersionFilter" && Array.isArray(variantFilters.releaseVersionFilter)) {
                    aSelectedKeys = variantFilters.releaseVersionFilter.map((item) => item.id);
                } else if ((filterName === "dueDateFilter" || filterName === "startDateFilter") && Array.isArray(variantFilters[filterName])) {
                    aSelectedKeys = Util.parseDateVariantValue(variantFilters[filterName]);
                    this.projectRelatedInfo[filterName].customDateRange = aSelectedKeys.customDateRange;
                } else {
                    aSelectedKeys = variantFilters[filterName] ? variantFilters[filterName] : [];
                }
                this.projectRelatedInfo[filterName].selectedIds = aSelectedKeys;

                this._filterControlSetup(oFilterControl, filterName, variantFilters, aSelectedKeys);
            }, this);

            this.getViewModel().setProperty(DISPLAYED_VIEW, sVariantFilters.taskListViewType || oConstant.LIST_VIEW_TYPE.taskTable);
        },

        _filterControlSetup: function (oFilterControl, filterName, variantFilters, aSelectedKeys) {
            if (filterName === "searchFilter") {
                oFilterControl.setValue(aSelectedKeys[0]);
                return;
            }
            this.projectRelatedInfo[filterName].forEach(function (item) {
                item.selected = this.projectRelatedInfo[filterName].selectedIds.indexOf(item.id) >= 0;
            }, this);
            if (oFilterControl instanceof MultiComboBox) {
                oFilterControl.setSelectedKeys(this.projectRelatedInfo[filterName].selectedIds);
            } else if (oFilterControl instanceof Select) {
                oFilterControl.clearSelection();
                oFilterControl.setSelectedKey(this.projectRelatedInfo[filterName].selectedIds[0]);
            } else if (oFilterControl instanceof DateRangeSelection) {
                Util.setDateRangeControl(oFilterControl, this.projectRelatedInfo[filterName].selectedIds);
            } else if (oFilterControl instanceof MultiInput) {
                // For timeboxFilter and releaseVersionFilter
                Util.setTokensOfMultiInput(oFilterControl, variantFilters[filterName] ? variantFilters[filterName] : [], this);
            } else if (oFilterControl instanceof Input) {
                // Set text value for start/due filter
                const sOptionText = this.projectRelatedInfo[filterName].filter((oItem) => oItem.selected).map((oItem) => oItem.text)[0];
                this._updateDateFilterValueText(oFilterControl, sOptionText, aSelectedKeys.customDateRange);
            }
        },

        _constructChartFilterModel: function (oInitialFilters) {
            // init fixed filter options
            this.projectRelatedInfo = {
                currentProject: "",
                myTeams: this.oModelsData.getDataOfMyTeamsFilter(),
                sorting: {},
                tableColumnSetting: oInitialFilters?.tableColumnSetting,
                searchFilter: oInitialFilters ? oInitialFilters.searchFilter : [],
                projectFilter: this.oModelsData.getDataOfProjectFilter(),
                crewFilter: this.oModelsData.getDataOfCrewFilter(),
                companyFilter: this.oModelsData.getDataOfCompanyFilter(),
                statusFilter: this.oModelsData.getDataOfStatusFilter(),
                subStatusFilter: ConstantModels.REQUIREMENT_SUB_STATUS,
                defectSubStatusFilter: this.oModelsData.getDefectSubStatuses(),
                qGateSubStatusFilter: this.oModelsData.getQualityGateSubStatuses(),
                approvalFilter: this.oModelsData.getRequirementApprovalStatusesFilter(),
                priorityFilter: this.oModelsData.getTaskPriorities(),
                storyPointsFilter: this.oModelsData.getDataOfStoryPointFilter(),
                subTasksCompletionFilter: this.oModelsData.getDataOfSubTasksCompletionFilter(),
                effortFilter: this.oModelsData.getDataOfEffortFilter(),
                taskTypeFilter: this.oModelsData.getTaskTypes(),
                tagFilter: this.oModelsData.getDataOfTagFilter(),
                workstreamFilter: this.oModelsData.getDataOfWorkstreamFilter(),
                timeboxFilter: this.getModel(oConstant.JSON_MODELS.timebox).getData(),
                startDateFilter: this.oModelsData.getDataOfStartDateFilter(),
                dueDateFilter: this.oModelsData.getDataOfDueDateFilter(),
                createdOnFilter: oInitialFilters ? oInitialFilters.createdOnFilter : [],
                lastChangedDateFilter: oInitialFilters ? oInitialFilters.lastChangedDateFilter : [],
                taskStateFilter: this.oModelsData.getDataOfTaskStateFilter(),
                taskTemplateFilter: this.oModelsData.getDataOfTaskTemplateFilter(),
                taskFavoriteFilter: this.oModelsData.getFavoriteFilterItems(),
                userTypeFilter: this.oModelsData.getUserTypesFilter(),
                testPlanFilter: this.oModelsData.getDataOfTestPlanFilter(oInitialFilters.testPlanFilter) // how to handle this dynamic filter?
            };

            // init dynamic filter options
            this.initDynamicFilterOptions();

            this.projectRelatedInfo.sorting = oInitialFilters.sorting;
            this.setModelWithSizeLimit(this.projectRelatedInfo, oConstant.JSON_MODELS.chartFilter);
        },

        initDynamicFilterOptions: function () {
            this.projectRelatedInfo.assigneeFilter = this.oModelsData.getDataOfAssigneeFilter();
            this.projectRelatedInfo.productFilter = this.oModelsData.getDataOfProductFilter();
            this.projectRelatedInfo.teamFilter = this.oModelsData.getDataOfProjectRoleFilter();
            this.projectRelatedInfo.solutionProcessFilter = this.oModelsData.getDataOfSolutionProcessFilter();
            this.projectRelatedInfo.pmHierarchyNodeFilter = this.oModelsData.getDataOfPmHierarchyNodeFilter();
            this.projectRelatedInfo.taskSourceFilter = this.oModelsData.getDataOfSourceFilter();
            this.projectRelatedInfo.releaseVersionFilter = this.oModelsData.getReleaseVersionsFilter();
            this.projectRelatedInfo.deliverableFilter = this.oModelsData.getDataOfDeliverableFilter();
            this.projectRelatedInfo.stakeholdersFilter = this.oModelsData.getDataOfStakeholdersFilter();
            this.projectRelatedInfo.createdByFilter = this.oModelsData.getDataOfCreatedByFilter();
            this.projectRelatedInfo.lastChangedByFilter = this.oModelsData.getDataOfLastChangedByFilter();
            this.projectRelatedInfo.externalReferenceFilter = this.oModelsData.getDataOfExternalReferenceFilter();
        },

        setupReleaseVersionModel: function () {
            // Release Version
            this.setModel(new JSONModel(this.oModelsData.getCurrentReleaseVersions(true)
                .concat([{
                    id: MASS_EDIT_KEEP_EXISTING_VALUE.id,
                    text: this.oBundle.getText(MASS_EDIT_KEEP_EXISTING_VALUE.i18nKey),
                    start_date: "-1"
                }])), oConstant.JSON_MODELS.projectReleaseVersion);
        },

        onCompanySelectionChanged: function (oEvent) {
            this.updateSelectedScopes(oEvent.getSource().getSelectedKeys());
        },

        onComboBoxSelectionChanged: function (oEvent) {
            const selectedKeys = oEvent.getSource().getSelectedKeys();
            const currentFilterItems = this.projectRelatedInfo[this.oControlFilterMap.get(oEvent.getSource())];
            currentFilterItems.forEach((item) => {
                item.selected = selectedKeys.indexOf(item.id) >= 0;
            });
            this.onFilterSelectionChanged();
            this.updateVariantStatus();
        },

        _validateDateRangeSelection: function (oDateRangeSelection) {
            if (!oDateRangeSelection.isValidValue()) {
                return false;
            }

            /*
             * Valid condition:
             * 1. No selection;
             * 2. Both "From" and "To" dates are selected.
             */
            const oStartDate = oDateRangeSelection.getDateValue();
            const oEndDate = oDateRangeSelection.getSecondDateValue();

            return (oStartDate && oEndDate) || (!oStartDate && !oEndDate);
        },

        onLastChangedDateSelectionChange: function (oEvent) {
            const oDateRangeSelection = oEvent.getSource();
            if (!this._validateDateRangeSelection(oDateRangeSelection)) {
                oDateRangeSelection.setValueState(CoreLibrary.ValueState.Error);
                oDateRangeSelection.setValueStateText(this.oBundle.getText("invalidLastChangeDateRange"));
                return;
            }

            oDateRangeSelection.setValueState(CoreLibrary.ValueState.None);

            const oStartDate = oDateRangeSelection.getDateValue();
            const oEndDate = oDateRangeSelection.getSecondDateValue();

            const aDateRange = (oStartDate && oEndDate) ? [{
                startDate: oStartDate,
                endDate: oEndDate
            }] : [];
            this.projectRelatedInfo[this.oControlFilterMap.get(oDateRangeSelection)] = aDateRange;

            this.onFilterSelectionChanged();
            this.updateVariantStatus();
        },

        updateVariantStatus: function () {
            this.oFilterBar.fireFilterChange();
        },

        closeDetailsPages: function () {
            const oMatchedRoute = this.oRouter.getRouteInfoByHash(this.oRouter.getHashChanger().getHash());
            if (!oMatchedRoute || oMatchedRoute.name !== oConstant.ROUTE_TASK_LIST) {
                this.oRouter.navTo(oConstant.ROUTE_TASK_LIST);
            }
        },

        onProjectSelectionChanged: function (oEvent, sTargetProjectGuid, bProjectMismatchBetweenListAndDetail) {
            if (oEvent) {
                // when trigger from UI, set variant modified and close other opened pages (detail/history/comment)
                this.updateVariantStatus();
                this.closeDetailsPages();

                // should ignore guid filter and clear info bar
                this._ignoreGuidFilterAndCloseInfoBar();
            }

            // when project context switched, clear navigated indicator and reset scroll position
            this.updateNavigatedIndicator();
            this.updateScrollPosition(0);

            const sSelectedProjectGuid = oEvent ? oEvent.getParameter("selectedItem").getKey() : sTargetProjectGuid;

            // update auth
            this.oCalmContextModel.setProperty("/authTask", this.oComponent.getModel(oConstant.JSON_MODELS.overallTaskAuth).getProperty(`/${sSelectedProjectGuid}`));

            // set global project context
            this.setGlobalSelectedProject(sSelectedProjectGuid);

            // busy page
            this.getViewModel().setProperty("/busy", true);

            const sTileId = this.oCalmContextModel.getProperty("/tileInfo/id");
            // update calmContext and UI model
            return Promise.all([
                this.oDataHandler.getProjectInfo(sSelectedProjectGuid, true), // update selectedProject of calmContext based on which all project specific filter options are built
                this.oDataHandler.getDynamicFilterOptions(sSelectedProjectGuid, sTileId),
                this.oDataHandler.getTaskDeliverables(sSelectedProjectGuid)
            ]).then((aContexts) => {
                this.refreshCbcTasks(aContexts[0]);
                this.oCalmContextModel.setProperty("/selectedProject", aContexts[0]);
                Project.init(aContexts[0]);
                this.oCalmContextModel.setProperty("/universalIdMap", DynamicFilterOptionHandler.parseUniversalIdMap(aContexts[1]));
                this.oCalmContextModel.setProperty("/filterOptions", DynamicFilterOptionHandler.parseDynamicFilterOptions(aContexts[1]));
                this.oCalmContextModel.setProperty("/allDeliverables", aContexts[2]);

                this.buildProjectModel(this.oCalmContextModel.getData().selectedProject);

                // If gantt chart was opened once, refresh timebox value of Gantt Chart every time project changed
                this.oGanttController?.setupLineModelsAndSyncTimeline(true);

                // when switching project by Control (oEvent is not undefined), we need to ignore variant because project stored in variant is different
                let oFilters;
                if (oEvent) {
                    oFilters = this.oFilterBarHandler.getInitialFiltersIgnoreAll();
                    this.getViewModel().setProperty(SEARCH_FIELD, "");
                } else {
                    oFilters = bProjectMismatchBetweenListAndDetail ? this.oFilterBarHandler.getInitialFiltersOnlyWithStartParameter() : this.oFilterBarHandler.getInitialFiltersOnlyIgnoreSessionStorage();
                }

                this._loadTaskTableData(oFilters);

                // expand filter area (updateScrollPosition takes time, it will overwrite header expand action if they execute together)
                this.getViewModel().setProperty(HEADER_EXPANDED, true);
            });
        },
        setGlobalSelectedProject: function (sSelectedProjectGuid) {
            DwcHandler.setSelectedProject(sSelectedProjectGuid);
        },

        onFilterSelectionChanged: function () {
            this.clearTableSelection();
            this._updateChartFilterModel();
            this._initTaskCreationOptions();
            this._updatePageSnappedTitle();

            this._refreshTaskTableIfNecessary();
            this._refreshAcceleratorViewIfNecessary();

            if (this.isGanttChartDisplayed()) {
                this.onGanttChartOpen();
            }

            if (this.isKanbanDisplayed()) {
                this.onKanbanOpen();
            }
        },

        displayInfoBarForNavigatedItem: function () {
            let sInfoBarText = "";
            if (Util.containsGuidFiltersInStartupParameters(this.oComponent.oStartupParams) && this.oComponent.oStartupParams.navDes) {
                // hightest priority with 'guid' parameter
                sInfoBarText = decodeURIComponent(this.oComponent.oStartupParams.navDes);
            } else if (this.oComponent.oStartupParams.objectId) {
                const oTaskItem = this._getTaskItems()[0];
                const oTask = oTaskItem.getBindingContext().getObject();
                oTaskItem.setNavigated(true);
                sInfoBarText = this.oBundle.getText(oConstant.TASK_TYPE_TO_FILTER_BY_TEXTMAP.get(oTask.type_id_v2), oTask.short_text);
            } else if (this.oComponent.oStartupParams.parentId) {
                sInfoBarText = this.oBundle.getText("filteredRequirementInfo", [decodeURIComponent(this.oComponent.oStartupParams.parentTitle)]);
            }

            const oViewModel = this.getViewModel();
            if (!oViewModel.getProperty(DISPLAY_INFO_BAR_FOR_FILTER)) {
                oViewModel.setProperty("/headerExpanded", false);
                oViewModel.setProperty(DISPLAY_INFO_BAR_FOR_FILTER, true);
            }

            this.byId("filteredTaskInfo").setText(sInfoBarText);
            this.byId("filteredTaskInfo").setTooltip(sInfoBarText);
            delete this.isNavFromOtherApps;
        },

        _generateAllFiltersMap: function () { // NOSONAR
            const oFiltersMap = new Map();

            if (this.bPermanentlyIgnoreGuidFilter || this.bIgnoreGuidFilter) {
                delete this.bIgnoreGuidFilter;
            } else {
                this.applyGuidFilterFromStartupParas(oFiltersMap);
            }

            Object.keys(TASK_FILTER_INFOS).forEach((filterName) => {
                const oFilter = this.projectRelatedInfo[filterName];

                // handle "empty option" like "not assigned"
                const nEmptyIndex = oFilter.selectedIds.indexOf(oConstant.NOT_ASSIGNED);
                if (nEmptyIndex >= 0) {
                    oFilter.selectedIds[nEmptyIndex] = null;
                }

                // handle "flag option" like "Me, My Roles"
                const nMeIndex = oFilter.selectedIds.indexOf(oConstant.ME_FLAG);
                if (nMeIndex >= 0) {
                    oFilter.selectedIds[nMeIndex] = this.oCurrentUserInfo.userId;
                }

                const nMyRolesIndex = oFilter.selectedIds.indexOf(oConstant.MY_TEAM_FLAG);
                if (nMyRolesIndex >= 0) {
                    if (this.projectRelatedInfo.myTeams.length === 0) {
                        // When my role is empty, push dummy ID, make sure no task filtered
                        oFilter.selectedIds[nMyRolesIndex] = DUMMY_TEAM_GUID;
                    } else {
                        oFilter.selectedIds.splice(nMyRolesIndex, 1);
                        this.projectRelatedInfo.myTeams.forEach((sTeamGuid) => oFilter.selectedIds.push(sTeamGuid));
                    }
                }
                oFiltersMap.set(filterName, structuredClone(oFilter.selectedIds));// make sure the splice operation in FilterHandler will not affect the original array
            });

            return oFiltersMap;
        },

        applyGuidFilterFromStartupParas: function (oFiltersMap) {
            const {
                objectId,
                parentId,
                guid
            } = this.oComponent.oStartupParams;

            const sId = objectId || parentId || guid;

            if (sId) {
                const sTargetGuid = Util.getGuidFromStartupIdParameter(sId, this.oCalmContextModel.getProperty("/universalIdMap"));
                if (parentId && sTargetGuid) {
                    // display sub items of given parent after navigate from other apps
                    oFiltersMap.set("parentGuidFilter", [sTargetGuid]);
                } else if (objectId && sTargetGuid) {
                    // display one specific item
                    oFiltersMap.set("taskGuidFilter", [sTargetGuid]);
                } else if (guid && sTargetGuid) {
                    // navigate from CA to display specific items
                    oFiltersMap.set("taskGuidFilter", sTargetGuid);
                }
                this.isNavFromOtherApps = true;
            }
        },

        _constructTaskFilter: function () {
            return FilterHandler.constructTaskFilterWithSearch(this._generateAllFiltersMap(), this.oCalmContextModel, this.getModel(oConstant.JSON_MODELS.favoriteTasks));
        },

        onUpdateFinished: function (oEvent) {
            // Set this flag for below scenarios:
            // - Creation/Edit/Comment/Copy page cannot be opened directly, check this flag to navigate to default route
            // - When navigate to default route, if list page fully loaded, avoid to load again, use binding refresh instead
            this.oComponent.listPageLoaded = true;

            this.endBusyInPage("beginColumnPages");

            const oViewModel = this.getViewModel();
            const nTaskCount = oEvent.getParameter("total");
            const sTitle = this.oBundle.getText("itemsNum", [nTaskCount]);
            oViewModel.setProperty(TABLE_TOTAL_COUNT, nTaskCount);
            oViewModel.setProperty("/tableTitle", sTitle);

            if (this.isNavFromOtherApps) {
                this.displayInfoBarForNavigatedItem();
            }

            if (oViewModel.getProperty(SHOULD_SELECT_GROWING_ITEMS)) {
                const aDeselectedPath = oViewModel.getProperty(TABLE_DESELECTED_ITEM_PATH);
                this._getTaskItems().forEach((item) => {
                    const oBingdingContext = item.getBindingContext();
                    if (oBingdingContext && oBingdingContext.getObject().state !== oConstant.ITEM_STATE.obsolete && aDeselectedPath.indexOf(oBingdingContext.getPath()) < 0) {
                        this.oTaskTable.setSelectedItem(item, true);
                    }
                });
            }
            // announce filtered items count
            this.oAccHandler.announce(this.oBundle.getText("IMP-PM-ENTRIES-FOUND", [nTaskCount]));
        },

        onTaskTableRowNavPress: function (oEvent) {
            this.oDataLossHandler.checkUnsavedChange(this.taskRowChange.bind(this, oEvent.getSource()));
        },

        taskRowChange: function (oSource) {
            const selectedItemId = oSource.getBindingContext().getProperty("formatted_universal_id");
            const oFCLModel = this.oComponent.getModel(oConstant.JSON_MODELS.fclStatus);
            const bReplace = oFCLModel.getProperty("/isThreeColumn") || oFCLModel.getProperty("/isTwoColumn") && this.isPageNotInHistory();

            this.oRouter.navTo(oConstant.ROUTE_TASK_DETAIL, {
                objectId: selectedItemId
            }, undefined, bReplace);

            // UX recommendation: show skeleton loading when navigating to detail page every time
            this.oEventBus.publish(this.oComponent.EVENT_CHANNEL, "showSkeletonLoading", {
                showSkeletonLoading: true
            });

            // record navigatedRowIndex and hand over to FCL's "columnResize" event
            this.navigatedRowIndex = this.oTaskTable.indexOfItem(oSource);
            // set flag and hand over to central "navigateAndScroll" function
            this.preventTableScroll = true;
            this.getViewModel().setProperty(HEADER_EXPANDED, false);
        },

        updateNavigatedIndicator: function (sCurrentSelectedId) {
            const oViewModel = this.getViewModel();
            oViewModel && oViewModel.setProperty("/taskListCurrentSelectedId", sCurrentSelectedId);
        },

        updateScrollPosition: function (nIndex) {
            if (nIndex >= 0 && nIndex < this.oTaskTable.getItems().length) {
                this.oTaskTable.scrollToIndex(nIndex);
            }
        },

        onColumnResize: function () {
            if (!this.oComponent.getModel(oConstant.JSON_MODELS.fclStatus).getProperty("/beginColumnVisible")) {
                return;
            }
            this.updateScrollPosition(this.navigatedRowIndex);
        },

        navigateAndScroll: function (sChannel, sEvent, oParam) {
            if (this.isKanbanDisplayed() && this.oClickedKanbanCard) {
                setTimeout(() => {
                    this.oScrollDelegate.scrollToElement(this.oClickedKanbanCard.getDomRef(), 0, [0, 0], true);
                }, 500);
                return;
            }

            this.updateNavigatedIndicator(oParam.objectId);

            // when manually select a task row in list page, do not scroll table.
            if (this.preventTableScroll) {
                delete this.preventTableScroll;
                return;
            }

            const oNavigatedItem = this._getTaskItems().find((oItem) => oItem.getBindingContext().getPath().indexOf(oParam.objectId) !== -1);
            if (oNavigatedItem) {
                this.navigatedRowIndex = this.oTaskTable.indexOfItem(oNavigatedItem);
                this.updateScrollPosition(this.navigatedRowIndex);
            }
        },

        _updateChartFilterModel: function () { // NOSONAR
            Object.keys(TASK_FILTER_INFOS).forEach(function (filterName) {
                const aItems = this.projectRelatedInfo[filterName];
                aItems.selectedIds = [];

                if ((filterName === "dueDateFilter" || filterName === "startDateFilter") && aItems.customDateRange) {
                    aItems.selectedIds.push(aItems.customDateRange);
                    return;
                }

                aItems.forEach((item) => {
                    if (filterName === "lastChangedDateFilter" || filterName === "createdOnFilter" || filterName === "searchFilter") {
                        // there is always only one item.
                        aItems.selectedIds.push(item);
                        return;
                    }
                    if (item.selected) {
                        let aSelectedItems = [];
                        if (item.id.split(";").length > 1) {
                            aSelectedItems = item.id.split(";").filter((selectedItem) => selectedItem !== "");
                        } else {
                            aSelectedItems.push(item.id);
                        }
                        aSelectedItems.forEach((singleId) => aItems.selectedIds.push(singleId));
                    }
                });

                if (filterName === "projectFilter" && aItems.selectedIds.length === 0) {
                    // Special handler for hide project
                    aItems.selectedIds.push(this.byId("projectSelect").getSelectedKey());
                }
            }, this);

            this.getModel(oConstant.JSON_MODELS.chartFilter).updateBindings(true);
        },

        _updatePageSnappedTitle: function () {
            const aSelectFilters = Object.keys(TASK_FILTER_INFOS).filter((filterName) => this.projectRelatedInfo[filterName]?.selectedIds?.length > 0);

            let sSnappedTitle;
            if (aSelectFilters.length > 0) {
                let sFilterSelected = `(${aSelectFilters.length}): `;
                const count = aSelectFilters.length < MAX_SHOW_SELECTED_FILTERS_NUMBER ? aSelectFilters.length : MAX_SHOW_SELECTED_FILTERS_NUMBER;
                for (let i = 0; i < count; i++) {
                    sFilterSelected += this.oBundle.getText(TASK_FILTER_INFOS[aSelectFilters[i]].i18nKey);
                    if (i !== count - 1) {
                        sFilterSelected += ", ";
                    }
                }
                // if filter count > 5, only 5 filters will be displayed and others will be shown as ...
                if (aSelectFilters.length > MAX_SHOW_SELECTED_FILTERS_NUMBER) {
                    sFilterSelected += ", ...";
                }
                sSnappedTitle = this.oBundle.getText("snappedTitle", [sFilterSelected]);
            } else {
                sSnappedTitle = this.oBundle.getText("filteredContentNone");
            }

            this.getViewModel().setProperty("/snappedTitle", sSnappedTitle);
        },

        setupTaskPredecessorModel: function () {
            const oPredecessorModel = new JSONModel({
                total: 0,
                listBusy: true,
                taskArray: []
            });
            this.setModel(oPredecessorModel, oConstant.JSON_MODELS.predecessorModel);
            return this.oDataHandler.getTaskPredecessor(this.context.getObject().guid).then((aTaskPredecessor) => {
                if (aTaskPredecessor) {
                    oPredecessorModel.setProperty("/total", aTaskPredecessor.length);
                    oPredecessorModel.setProperty("/taskArray", aTaskPredecessor);
                }
                oPredecessorModel.setProperty("/listBusy", false);
            });
        },

        setupTaskTagsModel: function (oPopover) {
            oPopover.setInitialFocus(null);
            const oTaskTagsModel = new JSONModel({
                busy: true,
                tags: []
            });
            this.setModel(oTaskTagsModel, oConstant.JSON_MODELS.taskTags);
            return this.oDataHandler.getTagsOfTask(this.context.getObject().guid).then(function (aTaskTags) {
                if (aTaskTags) {
                    aTaskTags.sort((a, b) => {
                        if (a.tag_label < b.tag_label) {
                            return -1;
                        }
                        if (a.tag_label > b.tag_label) {
                            return 1;
                        }
                        return 0;
                    });
                    oTaskTagsModel.setProperty("/tags", aTaskTags);
                }
            }).finally(() => {
                oTaskTagsModel.setProperty("/busy", false);
            });
        },

        onTaskPredecessorPress: function (oEvent) {
            if (this.oPredecessorPopover) {
                this.oPredecessorPopover.close();
            }

            this.onNavToTaskDetail(oEvent);
        },

        _handleCreateItemClicked: function (sType) {
            this.oDataLossHandler.checkUnsavedChange(this.openTaskCreatingColumn.bind(this, sType));
        },

        openTaskCreatingColumn: function (sType) {
            this.oRouter.navTo(oConstant.ROUTE_TASK_CREATION, {
                typeId: sType
            }, false);
        },

        createTaskFromTemplate: function (oTask) {
            this.oComponent.oTaskTemplate = oTask;
            this.openTaskCreatingColumn(oConstant.MAP_TASK_TYPE_TO_ARGS[oTask.type_id_v2]);
        },

        onTaskListSearchFieldChange: function (oEvent) {
            const oSearchField = oEvent.getSource();
            const sEventId = oEvent.getId();
            Util.delayedLiveSearch("taskListSearchTimeout", sEventId, () => {
                const sFilterName = this.oControlFilterMap.get(oSearchField);
                const sCurrentSearchText = this._getLimitedSearchText();
                this.projectRelatedInfo[sFilterName] = sCurrentSearchText ? [sCurrentSearchText] : [];
                this.onFilterSelectionChanged();
                this.updateVariantStatus();
            }, 800);
        },

        _getLimitedSearchText: function () {
            const searchText = this.getViewModel().getProperty(SEARCH_FIELD);
            return searchText.split(" ").slice(0, 20).join(" ");
        },

        handleMassEditButtonPressed: function () {
            this.oDataLossHandler.checkUnsavedChange(this.oMassEditHandler.onMassEditButtonPressed.bind(this.oMassEditHandler), undefined, undefined, this.exitTaskEditMode.bind(this));
        },

        handleMassDeleteButtonPressed: function () {
            this.oDataLossHandler.checkUnsavedChange(this.oMassDeleteHandler.onMassDeleteButtonPressed.bind(this.oMassDeleteHandler), undefined, undefined, this.exitTaskEditMode.bind(this));
        },

        onReqDeleteBtnPress: function (oEvent) {
            const oDelBtn = oEvent.getSource();
            const bIsDeletedAssociatedTask = oDelBtn.getModel(oDelBtn.data("modelName"))?.getProperty("/isDeletedAssociatedTask");
            this._batchDeleteManualTask(this.aDeletableItems, bIsDeletedAssociatedTask);
            oEvent.getSource().getParent().close();
        },

        onReqDeleteBtnCancel: function (oEvent) {
            oEvent.getSource().getParent().close();
        },

        _batchDeleteManualTask: function (aManualTasks, bIsDeletedAssociatedTask = false) {
            this.setAppBusy(true);

            const sTaskType = aManualTasks[0].type_id_v2 === oConstant.TASK_MASTER_TYPE.requirement
                || aManualTasks.length < 2 ? aManualTasks[0].type_id_v2 : "";
            const aManualTasksGuids = aManualTasks.map((item) => item.guid);
            return this.oDataHandler.batchDeleteTasksWithUserOption(aManualTasksGuids, bIsDeletedAssociatedTask).then(() => {
                this.postTaskDeleted(undefined, undefined, {
                    deletedTaskNumber: aManualTasks.length,
                    taskType: sTaskType
                });
                this.oEventBus.publish(this.oComponent.EVENT_CHANNEL, "taskDeletedInList", {
                    aDeletedTaskGuids: aManualTasksGuids
                });
                this.clearTableSelection();
                this.resetItemLoadedFlag();
            }).catch((oError) => {
                this.handleTaskOperationError(oError);
            }).finally(() => {
                this.setAppBusy(false);
            });
        },

        _getSingleUserStoryDeletedTooltipOfChildTaskAmount: function (nChildTaskAmount) {
            switch (nChildTaskAmount) {
                case 0:
                    return this.oBundle.getText("userStoryDeletedSuccessful");
                case 1:
                    return this.oBundle.getText("userStoryWithSingleSubTaskDeletedSuccessful");
                default:
                    return this.oBundle.getText("userStoryWithMultipleSubTasksDeletedSuccessful");
            }
        },

        postTaskDeleted: function (sChannel, sEvent, oParam) {
            this.refreshTaskTable();
            let sMessage = "";
            switch (oParam.taskType) {
                case oConstant.TASK_MASTER_TYPE.requirement:
                    sMessage = oParam.deletedTaskNumber === 1 ? this.oBundle.getText("requirementDeletedSuccessful")
                        : this.oBundle.getText("multipleRequirementsDeletedSuccessful", [oParam.deletedTaskNumber]);
                    break;
                case oConstant.TASK_MASTER_TYPE.projectTaskV2:
                case oConstant.TASK_MASTER_TYPE.subTaskV2:
                    sMessage = this.oBundle
                        .getText(oConstant.DELETE_MESSAGE_KEY[oParam.taskType].singleDeleteSuccess);
                    break;
                case oConstant.TASK_MASTER_TYPE.userStoryV2:
                    sMessage = this._getSingleUserStoryDeletedTooltipOfChildTaskAmount(oParam.childTaskAmount);
                    break;
                default:
                    sMessage = this.oBundle.getText(oParam.deletedTaskNumber > 1 ? "itemsDeletedSuccessful" : "taskDeletedSuccessful");
                    break;
            }

            MessageToast.show(sMessage);
        },

        onTimeboxKeepValueSelected: function () {
            const oModel = this.getModel(oConstant.JSON_MODELS.timeboxDialogModel);
            oModel.setProperty("/selectedTimebox", MASS_EDIT_KEEP_EXISTING_VALUE.id);
            oModel.setProperty("/selectedTimeboxType", null);
            oModel.setProperty("/selectedCount", 0);
            this.oMassEditHandler.updateTimeboxMassEditModel(null, oConstant.MASS_EDIT_KEEP_EXISTING_VALUE.id);
            this._oTimeboxUpdatePopover.close();
        },

        onSelectedReleaseVersionChanged: function (oEvent) {
            const oItem = oEvent.getParameter("listItem").getBindingContext("projectReleaseVersion");
            const id = oItem.getProperty("id");
            this.getViewModel().setProperty("/releaseVersionValue", oItem.getProperty("text"));
            this.getModel(oConstant.JSON_MODELS.massEditModel).setProperty("/data/ex_release_version_id", id === oConstant.NOT_ASSIGNED ? null : id);
            oEvent.getSource().getParent().close();
        },

        clearTableSelection: function () {
            const oViewModel = this.getViewModel();
            oViewModel.setProperty(SHOULD_SELECT_GROWING_ITEMS, false);
            oViewModel.setProperty(TABLE_DESELECTED_ITEM_PATH, []);
            oViewModel.setProperty(MASS_EDIT_BTN_ENABLE, false);
            oViewModel.setProperty(MASS_DELETE_BTN_ENABLE, false);
            oViewModel.setProperty(DISPLAY_INFO_BAR_FOR_SELECTION, false);

            delete this.aSelectedTaskListCache;
            this.oTaskTable.removeSelections(true);

            if (this.isGanttChartDisplayed()) {
                this.oGanttController?.clearTableSelection();
            }
        },

        _initTaskCreationOptions: function (isForKanbanView = false) {
            const oOptionsModel = oCreationOptionHandler.buildCreationOptionsModel(this.projectRelatedInfo.taskTypeFilter.selectedIds, this.oBundle, this.oCalmContextModel.getProperty("/tileInfo/id"), isForKanbanView);
            this.setModel(oOptionsModel, oConstant.JSON_MODELS.createTaskOptionsModel);
            // Do NOT enable the 'Create' button if there is no option or only 'From Template' option
            this.getViewModel().setProperty("/createBtnEnable", oOptionsModel.getData().length > 1);
        },

        onTaskTableSelectionChange: async function (oEvent) {
            let aDeselectPath;
            const oViewModel = this.getViewModel();
            if (oEvent.getParameters().selectAll) { // select all
                oViewModel.setProperty(SHOULD_SELECT_GROWING_ITEMS, true);
                oViewModel.setProperty(TABLE_DESELECTED_ITEM_PATH, []);
            } else if (oEvent.getParameters().listItems.length > 1) { // deselect all
                oViewModel.setProperty(SHOULD_SELECT_GROWING_ITEMS, false);
            } else if (oEvent.getParameters().selected) { // select one
                aDeselectPath = oViewModel.getProperty(TABLE_DESELECTED_ITEM_PATH);
                const index = aDeselectPath.indexOf(oEvent.getParameters().listItems[0].getBindingContext().getPath());
                if (index > -1) {
                    aDeselectPath.splice(index, 1);
                }
                oViewModel.setProperty(TABLE_DESELECTED_ITEM_PATH, aDeselectPath);
            } else if (!oEvent.getParameters().selected) { // deselect one
                aDeselectPath = oViewModel.getProperty(TABLE_DESELECTED_ITEM_PATH);
                aDeselectPath.push(oEvent.getParameters().listItems[0].getBindingContext().getPath());
                oViewModel.setProperty(TABLE_DESELECTED_ITEM_PATH, aDeselectPath);
            }

            const aTasks = await this._getSelectedTaskList();
            this._setMassEditAndMassDeleteEnable(aTasks);
            this._displayInfoBarForTaskSelection(aTasks.length);
        },

        _setMassEditAndMassDeleteEnable: function (aTasks) {
            if (!this.oMassEditHandler) {
                this.oMassEditHandler = new MassEditHandler(this);
            }

            if (!this.oMassDeleteHandler) {
                this.oMassDeleteHandler = new MassDeleteHandler(this);
            }

            this.oMassEditHandler.updateMassEditButtonEnabled(aTasks, oConstant.LIST_VIEW_TYPE.taskTable);
            this.oMassDeleteHandler.updateMassDeleteButtonEnabled(aTasks);
        },

        _getSelectedTaskList: function () {
            if (this.getViewModel().getProperty(SHOULD_SELECT_GROWING_ITEMS)) {
                return this._getWholeSelectedTaskList();
            }
            return Promise.resolve(this.oTaskTable.getSelectedItems().map((item) => item.getBindingContext().getObject()));
        },

        _getWholeSelectedTaskList: function () {
            const fnHandleWholeTasks = (aTasks) => {
                const aDeselectedTasks = this.getViewModel().getProperty(TABLE_DESELECTED_ITEM_PATH);
                return aTasks.filter((oContext) => aDeselectedTasks.indexOf(oContext.getPath()) < 0)
                    .map((oContext) => oContext.getObject());
            };

            if (this.aSelectedTaskListCache) {
                return Promise.resolve(fnHandleWholeTasks(this.aSelectedTaskListCache));
            }

            const oViewModel = this.getViewModel();
            oViewModel.setProperty("/busy", true);
            const aTaskFilter = this._constructTaskFilter();
            return this.oDataHandler.getFilteredTasks(aTaskFilter, this.projectRelatedInfo.sorting.sorter).then((oWholeTasks) => {
                this.aSelectedTaskListCache = oWholeTasks;
                return fnHandleWholeTasks(oWholeTasks);
            }).finally(() => {
                oViewModel.setProperty("/busy", false);
            });
        },

        _displayInfoBarForTaskSelection: function (iTasksLength) {
            const iCount = this.getViewModel().getProperty(TABLE_TOTAL_COUNT);
            const sInfoBarText = this.oBundle.getText("selectedItemsInfo", [iTasksLength, iCount]);
            this.getViewModel().setProperty(DISPLAY_INFO_BAR_FOR_SELECTION, iTasksLength > 0);
            this.byId("selectedTasksInfo").setText(sInfoBarText);
            this.byId("selectedTasksInfo").setTooltip(sInfoBarText);
        },

        onExport: function () {
            ExportHandler.exportAllTasksToExcel(this);
        },

        onExportTemplate: function (bCreating) {
            ExportHandler.exportTasksAsTemplate(this, bCreating);
        },

        _setUploadDialogModel: function () {
            const oProject = this.oCalmContextModel.getProperty("/selectedProject");
            const sProjectName = this.oUnescapedString.formatValue(oProject.name);
            const data = {
                title: this.oBundle.getText("uploadManualTasks"),
                content1: this.oBundle.getText("uploadManualTaskText1", [sProjectName]),
                content2: this.oBundle.getText("uploadManualTaskText2"),
                uploadUrl: `/ui/task-management-service-ui/v1/api/v1/excel/upload/${oProject.guid}`,
                manualUploadFileBtnEnabled: false
            };
            this.setModel(new JSONModel(data), oConstant.JSON_MODELS.uploadDialogModel);
        },

        _refreshWorkstreams: function () {
            return this.oDataHandler.getTaskWorkstreams(true).then((oWorkstreamResponse) => {
                this.oCalmContextModel.setProperty(WORKSTEAMS, oWorkstreamResponse.value);
                this.projectRelatedInfo.workstreamFilter = this.oModelsData.getDataOfWorkstreamFilter();
                this.projectRelatedInfo.workstreamFilter.selectedIds = [];
                this.getModel(oConstant.JSON_MODELS.chartFilter).updateBindings();
            });
        },

        refreshAfterUploadSuccess: function () {
            this.oEventBus.publish(this.oComponent.EVENT_CHANNEL, "taskUpdated");
            this.oEventBus.publish(this.oComponent.EVENT_CHANNEL, "refreshTags");
            this.resetItemLoadedFlag();
            // Delivaerables and sources maybe changed and new tasks maybe created during excel uploading
            this._refreshChangedContext();
            this._refreshWorkstreams();
        },

        _refreshChangedContext: function () {
            const sCurrentProjectGuid = this.oCalmContextModel.getProperty(SELECTED_PROJECT_GUID);
            const sTileId = this.oCalmContextModel.getProperty("/tileInfo/id");
            return Promise.all([
                this.oDataHandler.getDynamicFilterOptions(sCurrentProjectGuid, sTileId),
                this.oDataHandler.getTaskDeliverables(sCurrentProjectGuid)
            ]).then((aRes) => {
                const oFilterOptions = aRes[0];
                const aAllDeliverables = aRes[1];

                // update deliverable related model
                this.setModel(new JSONModel(aAllDeliverables), oConstant.JSON_MODELS.deliverableModel);
                this.oCalmContextModel.setProperty("/allDeliverables", aAllDeliverables);

                // Universe id to task guid mapping and dynamic filter options
                this.oCalmContextModel.setProperty("/universalIdMap", DynamicFilterOptionHandler.parseUniversalIdMap(oFilterOptions));
                this.oCalmContextModel.setProperty("/filterOptions", DynamicFilterOptionHandler.parseDynamicFilterOptions(oFilterOptions));

                // init dynamic filter options
                this.initDynamicFilterOptions();
                this.handleSelectedIds();

                // update bindings for chart filter model
                this.getModel(oConstant.JSON_MODELS.chartFilter).updateBindings();
            });
        },

        handleSelectedIds: function () {
            this.projectRelatedInfo.deliverableFilter.selectedIds = [];
            this.projectRelatedInfo.taskSourceFilter.selectedIds = [];
            this.projectRelatedInfo.solutionProcessFilter.selectedIds = [];
            this.projectRelatedInfo.externalReferenceFilter.selectedIds = [];
        },

        onUpdateStarted: function (oEvent) {
            if (oEvent.getSource().getMode() === "MultiSelect" && oEvent.getParameter("reason") === "Refresh") {
                this.getViewModel().setProperty(MASS_EDIT_BTN_ENABLE, false);
                this.oTaskTable.setSelectedContextPaths([]);
            }
        },

        onOpenComments: function (oEvent) {
            this.oDataLossHandler.checkUnsavedChange(this.openCommentsColumn.bind(this, oEvent.getSource()));
        },

        openCommentsColumn: function (oSource) {
            const bReplace = !!(this.oComponent.getModel(oConstant.JSON_MODELS.fclStatus).getProperty("/isTwoColumn") && this.isPageNotInHistory());
            this.oRouter.navTo(oConstant.ROUTE_TASK_COMMENT, {
                objectId: oSource.getBindingContext().getProperty("guid")
            }, undefined, bReplace);
        },

        _getTaskItems: function () {
            return this.oTaskTable.getItems().filter((oItem) => !(oItem instanceof GroupHeaderListItem));
        },

        onGanttChartOpen: function () {
            this.getViewModel().setProperty(DISPLAYED_VIEW, oConstant.LIST_VIEW_TYPE.ganttChart);

            this._adjustStyle();

            if (!this.oGanttController) {
                this.oGanttController = new GanttChartController(this);
                this.oGanttHandler = this.oGanttController.oHandler;
                this.oGanttFragmentLoadedPromise = this.oGanttController.loadGanttChart();
            }

            // Loading data
            this.loadGanttChartData();
        },

        getReleaseVersionFilterHeader: function (oGroup) {
            let sTitle;
            switch (oGroup.key) {
                case oConstant.RELEASE_VERSION_FILTER_GROUP_HEADER.notAssigned:
                    sTitle = this.oBundle.getText("invisibleTextItemEmpty");
                    break;
                case oConstant.RELEASE_VERSION_FILTER_GROUP_HEADER.currentRelease:
                    sTitle = this.oBundle.getText("currentDeploymentPlan");
                    break;
                case oConstant.RELEASE_VERSION_FILTER_GROUP_HEADER.previousReleases:
                    sTitle = this.oBundle.getText("previousDeploymentPlan");
                    break;
                default:
                    break;
            }

            return new GroupHeaderListItem({
                title: sTitle
            });
        },

        onTaskTableOpen: function () {
            this.getViewModel().setProperty(DISPLAYED_VIEW, oConstant.LIST_VIEW_TYPE.taskTable);

            this._adjustStyle();
            this.oP13nEngineHandler.bindTaskTableByCurrentSetting();
            this._initTaskCreationOptions();
        },

        loadKanbanData: async function () {
            const oViewModel = this.getViewModel();
            oViewModel.setProperty("/busy", true);
            const aTaskFilter = FilterHandler.constructKanbanFilterWithSearch(this._generateAllFiltersMap(), this.oCalmContextModel, this.getModel(oConstant.JSON_MODELS.favoriteTasks));
            const aKanbanTasks = await this.oDataHandler.getKanbanTasks(aTaskFilter);

            this.endBusyInPage("beginColumnPages");

            oViewModel.setProperty("/kanbanTitle", this.oBundle.getText("itemsNum", [aKanbanTasks.length]));
            this.setModelWithSizeLimit(aKanbanTasks, oConstant.JSON_MODELS.kanbanTasks);
            this.oComponent.listPageLoaded = true;
            this.oSessionStorageHandler.setSessionStorage();
        },

        loadGanttChartData: async function (syncToday = true) {
            const oViewModel = this.getViewModel();
            oViewModel.setProperty("/busy", true);

            const aTimeboxes = this.getModel(oConstant.JSON_MODELS.timebox).getData().slice();
            const aDeliverables = this.oCalmContextModel.getProperty("/filterOptions/deliverables").slice();

            try {
                const aResults = await Promise.all([
                    this.oGanttFragmentLoadedPromise,
                    this.oDataHandler.getGanttChartTasks(this._constructTaskFilter())
                ]);
                this.oGanttController.setupGanttChartDataModel(aTimeboxes, aDeliverables, aResults[1], syncToday);
                this.oComponent.listPageLoaded = true;
                // update task count in title
                oViewModel.setProperty("/ganttTableTitle", this.oBundle.getText("itemsNum", [this.oGanttHandler.getCount()]));
            } finally {
                this.endBusyInPage("beginColumnPages");

                this.oSessionStorageHandler.setSessionStorage();
            }
        },

        loadAcceleratorViewData: function () {
            const oViewModel = this.getViewModel();
            oViewModel.setProperty("/busy", true);

            const aTimeboxes = this.getModel(oConstant.JSON_MODELS.timebox).getData().slice();
            const oFilter = FilterHandler.constructAcceleratorFilterWithSearch(this._generateAllFiltersMap(), this.oCalmContextModel, this.getModel(oConstant.JSON_MODELS.favoriteTasks));
            this.oDataHandler.getFilteredTasks4AcceleratorView(oFilter).then((aTasks) => {
                const data = AcceleratorViewHandler.build(aTimeboxes, aTasks, this.oBundle);
                this.setModel(new JSONModel(data), oConstant.JSON_MODELS.accelerators);
            }).finally(() => oViewModel.setProperty("/busy", false));
        },

        _buildDateFilterModel: function (sFilterInputId) {
            const bIsDueFilter = sFilterInputId.indexOf("dueDateInput") >= 0;
            const sFilterName = bIsDueFilter ? "dueDateFilter" : "startDateFilter";
            let sSelectedType = oConstant.FILTER_DATE_TYPE.predefined;
            let nSelectedIndex = -1;
            let oStartDate = null;
            let oEndDate = null;
            let bEnableClearBtn = false;

            // Variable "this.projectRelatedInfo" can be "undefined" when task list controller initialization has not been finished.
            const aPredefinedDateSelection = this.projectRelatedInfo ? this.projectRelatedInfo[sFilterName] : [];
            if (aPredefinedDateSelection.customDateRange && aPredefinedDateSelection.customDateRange.startDate && aPredefinedDateSelection.customDateRange.endDate) {
                oStartDate = aPredefinedDateSelection.customDateRange.startDate;
                oEndDate = aPredefinedDateSelection.customDateRange.endDate;

                sSelectedType = oConstant.FILTER_DATE_TYPE.custom;
                bEnableClearBtn = true;
            } else {
                aPredefinedDateSelection.forEach((oItem, nIndex) => {
                    if (oItem.selected) {
                        nSelectedIndex = nIndex;
                        bEnableClearBtn = true;
                    }
                });
            }
            const sStartTitle = "startLabel";
            const sDueTitle = "plannedCompletionLabel";
            this.getModel(oConstant.JSON_MODELS.dateFilterModel).setData({
                filterName: sFilterName,
                sourceControlId: sFilterInputId,
                title: this.oBundle.getText(bIsDueFilter ? sDueTitle : sStartTitle),
                selectedType: sSelectedType,
                predefinedDateSelection: aPredefinedDateSelection,
                selectedItemIndex: nSelectedIndex,
                customStartDate: oStartDate,
                customEndDate: oEndDate,
                enableClearBtn: bEnableClearBtn
            });
        },

        openDateFilterDialog: function (oEvent) {
            const oDateInput = oEvent.getSource();

            this._buildDateFilterModel(oDateInput.getId());

            this._setupPopover(oDateInput._getValueHelpIcon ? oDateInput._getValueHelpIcon() : oDateInput,
                this._oDateFilterDialog,
                this.pagePrefix(),
                "DateFilterPopover",
                null)
                .then(function (oDialog) {
                    if (oDialog) {
                        this._oDateFilterDialog = oDialog;
                    }
                }.bind(this));
        },

        onPredefinedDateFilterSelected: function () {
            // If user selects a predefined option, ignore the custom selection
            const dateFilterModel = this.getModel(oConstant.JSON_MODELS.dateFilterModel);
            dateFilterModel.setProperty("/customStartDate", null);
            dateFilterModel.setProperty("/customEndDate", null);
            dateFilterModel.setProperty(ENABLE_CLEAR_BTN, true);
        },

        onCustomDateCalendarSelected: function () {
            // If user uses custom selection, ignore the predefined option
            const dateFilterModel = this.getModel(oConstant.JSON_MODELS.dateFilterModel);
            dateFilterModel.setProperty("/selectedItemIndex", -1);
            dateFilterModel.setProperty(ENABLE_CLEAR_BTN, true);
        },

        _updateDateFilterValueText: function (oDateFilterInput, sSelectedOptionText, oCustomDateRange) {
            const sCustomDateRangeText = oCustomDateRange ? DateTimeUtil.formatAsDateRange(oCustomDateRange.startDate, oCustomDateRange.endDate) : null;
            Util.setDateFilterTextValue(oDateFilterInput, sSelectedOptionText, sCustomDateRangeText);
        },

        _setFilterDataForVariant: function (sFilterName, sSelectedOptionKey, oCustomDateRange) {
            const oFilterControl = this.oControlFilterMap.get(sFilterName);
            oFilterControl.setSelectedKey(oCustomDateRange ? `${CoreFormatter.formatDateForVariant(oCustomDateRange.startDate)} | ${CoreFormatter.formatDateForVariant(oCustomDateRange.endDate)}` : sSelectedOptionKey); // NOSONAR
        },

        onDateFilterSelected: function () {
            const dateFilterData = this.getModel(oConstant.JSON_MODELS.dateFilterModel).getData();
            const sFilterName = dateFilterData.filterName;

            delete this.projectRelatedInfo[sFilterName].customDateRange;

            let oSelectedOption = {};
            let oCustomDateRange = null;
            if (dateFilterData.customStartDate && dateFilterData.customEndDate) {
                oCustomDateRange = {
                    startDate: dateFilterData.customStartDate,
                    endDate: dateFilterData.customEndDate
                };

                this.projectRelatedInfo[sFilterName].customDateRange = oCustomDateRange;
            }

            dateFilterData.predefinedDateSelection.forEach((oItem, nIndex) => {
                if (nIndex === dateFilterData.selectedItemIndex) {
                    oItem.selected = true;
                    oSelectedOption = oItem;
                } else {
                    oItem.selected = false;
                }
            });

            this._setFilterDataForVariant(sFilterName, oSelectedOption.id, oCustomDateRange);
            this._updateDateFilterValueText(this.oControlFilterMap.get(sFilterName), oSelectedOption.text, oCustomDateRange);

            this._oDateFilterDialog.close();
            this.onFilterSelectionChanged();
            this.updateVariantStatus();
        },

        onDateFilterSelectionCanceled: function () {
            this._oDateFilterDialog.close();
        },

        onDateFilterSelectionCleared: function () {
            const dateFilterModel = this.getModel(oConstant.JSON_MODELS.dateFilterModel);
            dateFilterModel.setProperty("/selectedItemIndex", -1);
            dateFilterModel.setProperty("/customStartDate", null);
            dateFilterModel.setProperty("/customEndDate", null);
            dateFilterModel.setProperty(ENABLE_CLEAR_BTN, false);
        },

        onReleaseVersionInputLiveChange: function (oEvent) {
            const aValidText = this.getModel(oConstant.JSON_MODELS.chartFilter).getProperty("/releaseVersionFilter").map((e) => e.text);
            const sCurrentValue = oEvent.getParameter("value");
            oEvent.getSource().setValueState(!sCurrentValue || (aValidText.some((sText) => sText.includes(sCurrentValue))) ? CoreLibrary.ValueState.None : CoreLibrary.ValueState.Error);
        },

        onReleaseVersionTokenUpdate: function (oEvent) {
            const aAddedTokenKeys = oEvent.getParameter("addedTokens").map((e) => e.getKey());
            const aRemovedTokenKeys = oEvent.getParameter("removedTokens").map((e) => e.getKey());

            this.getModel(oConstant.JSON_MODELS.chartFilter).getProperty("/releaseVersionFilter").forEach((item) => {
                if (aAddedTokenKeys.includes(item.id)) {
                    item.selected = true;
                } else if (aRemovedTokenKeys.includes(item.id)) {
                    item.selected = false;
                }
            });
            this.onFilterSelectionChanged();
            this.updateVariantStatus();
        },

        onReleaseVersionSelectionChanged: function (oEvent) {
            const aSelectedItems = oEvent.getSource().getSelectedItems();
            const aSelectedKeys = aSelectedItems.map((item) => item.getBindingContext("chartFilter").getProperty("id"));
            const currentFilterItems = this.projectRelatedInfo.releaseVersionFilter;
            currentFilterItems.forEach((item) => {
                item.selected = aSelectedKeys.indexOf(item.id) >= 0;
            });
            this.onFilterSelectionChanged();
            this.updateVariantStatus();

            Util.setTokensOfMultiInput(this.byId("releaseVersionFilterMultiInput"), currentFilterItems.filter((oItem) => oItem.selected), this);
        },

        onDelReleaseVersionFilterToken: function (oEvent) {
            const sKey = oEvent.getSource().getKey();
            const aAllReleaseVersions = this.projectRelatedInfo.releaseVersionFilter;
            const aDeselectedItems = aAllReleaseVersions ? aAllReleaseVersions.filter((oItem) => oItem.id === sKey) : [];
            if (aDeselectedItems.length > 0) {
                aDeselectedItems[0].selected = false;
                this.onFilterSelectionChanged();
                this.updateVariantStatus();
            }
            setTimeout(this.updateVariantStatus.bind(this), 100);
        },

        onTriggerTaskCreationOption: async function (oEvent) {
            this._oCreateTaskOptionPopover = await this._setupPopover(oEvent.getSource(), this._oCreateTaskOptionPopover, this.pagePrefix(), "createTask.CreateTaskPopover");
        },

        onGenerateReqPress: async function () {
            this.getViewModel().setProperty("/reqGenerateDialogBusy", true);
            await Util.openSimpleDialog(this.getView().getId(), "com.sap.calm.imp.tkm.ui.fragment.reqGeneration.ReqGenerationDialog", this);

            const sProjectId = this.oCalmContextModel.getProperty(SELECTED_PROJECT_GUID);
            const aTemplates = await this.oDataHandler.getTemplateTasks(sProjectId, [oConstant.TASK_MASTER_TYPE.requirement]);
            const aScopes = this.oCalmContextModel.getProperty("/selectedProject/to_companies").sort((a, b) => Sorter.alphabetSorterWithOrder(a.company_name, b.company_name));
            this.setModel(new JSONModel({ scopeId: aScopes[0]?.company_id, scopeName: aScopes[0]?.company_name, enableSapHelpDocumentation: false, templates: aTemplates, templateRequirementId: aTemplates[0]?.guid, documentIds: [] }), oConstant.JSON_MODELS.reqGenerationModel);
            const oGenerateReqProgressIndicator = this.byId("idGenerateReqProgressIndicator");
            // const oGenerateBtn = this.byId("idGenerateButton");
            oGenerateReqProgressIndicator.setVisible(false);
            // oGenerateBtn.setEnabled(true);
            this.getViewModel().setProperty("/reqGenerateDialogBusy", false);
        },

        onSolutionProcessValueHelpPress: function () {
            this._oProcessMassEditSelectDialogHandler ??= new ProcessSelectionController(this);
            this._oProcessMassEditSelectDialogHandler.openAssignDialog();
        },

        onDocumentValueHelpRequest: function (oEvent) {
            this.oDocumentMultiInput = oEvent.getSource();
            this._oDocumentSelectDialogHandler ??= new DocumentSelectionController(this);
            this._oDocumentSelectDialogHandler.openAssignDialog(this.oDocumentMultiInput.getValue());
        },

        onDocumentTokenUpdate: function (oEvent) {
            const aAddedTokens = oEvent.getParameter("addedTokens");
            const aRemovedTokenKeys = oEvent.getParameter("removedTokens").map((e) => e.getKey());
            const oReqGenerationModel = this.getModel(oConstant.JSON_MODELS.reqGenerationModel);
            const oDocumentData = oReqGenerationModel.getProperty("/documentIds");

            const aDocumentsAfterRemoved = oDocumentData.filter((item) => !aRemovedTokenKeys.includes(item));
            const aDocumentsToBeAdded = aAddedTokens.filter(((item) => !aDocumentsAfterRemoved.some((docId) => docId === item.getKey()))).map((token) => token.getKey());
            oReqGenerationModel.setProperty("/documentIds", aDocumentsAfterRemoved.concat(aDocumentsToBeAdded));

            const oDocumentMultiInput = oEvent.getSource();
            oDocumentMultiInput.setTokens(oDocumentMultiInput.getTokens().sort((a, b) => a.getText().toLowerCase().localeCompare(b.getText().toLowerCase())));
        },

        onDocumentSelectionUpdated: function (sChannel, sEvent, oParam) {
            oParam.contexts?.forEach((context) => {
                const oDocumentData = context.getObject();
                const oToken = new Token({ key: oDocumentData.uuid, text: new UnescapedString().formatValue(oDocumentData.title) });
                this.oDocumentMultiInput.addToken(oToken);
            });
            this.oDocumentMultiInput.setTokens(this.oDocumentMultiInput.getTokens().sort((a, b) => a.getText().toLowerCase().localeCompare(b.getText().toLowerCase())));
        },

        onScopeSelectChange: function (oEvent) {
            const sScopeName = oEvent.getParameter("selectedItem").getText();
            const oReqGenerationModel = this.getModel(oConstant.JSON_MODELS.reqGenerationModel);
            // clear solution process selection
            oReqGenerationModel.setProperty("/solutionProcess", "");
            oReqGenerationModel.setProperty("/scopeName", sScopeName);
            oReqGenerationModel.updateBindings(true);
        },

        onGenerateBtnPress: function (oEvent) {
            this.getViewModel().setProperty("/reqGenerateDialogBusy", true);
            const oGenerateReqProgressIndicator = this.byId("idGenerateReqProgressIndicator");
            oGenerateReqProgressIndicator.setVisible(true);
            // const oGenerateBtn = this.byId("idGenerateButton");
            // oGenerateBtn.setEnabled(false);
            const oReqGenerationModel = this.getModel(oConstant.JSON_MODELS.reqGenerationModel);
            const oPayload = {
                PROJECT_ID: this.oCalmContextModel.getProperty(SELECTED_PROJECT_GUID),
                SCOPE_ID: oReqGenerationModel.getProperty("/scopeId"),
                SCOPE_NAME: oReqGenerationModel.getProperty("/scopeName"),
                SOLUTION_PROCESS_ID: oReqGenerationModel.getProperty("/solutionProcess/id"),
                CONTENT_PACKAGE_VERSION_ID: oReqGenerationModel.getProperty("/solutionProcess/contentPackageVersionId"),
                DOCUMENT_IDS: oReqGenerationModel.getProperty("/documentIds"),
                REQUIREMENT_TITLE: oReqGenerationModel.getProperty("/requirementTitle"),
                TEMPLATE_REQUIREMENT_ID: oReqGenerationModel.getProperty("/templateRequirementId"),
                ENABLE_SAP_HELP_DOCUMENTATION: oReqGenerationModel.getProperty("/enableSapHelpDocumentation")
            };

            new Promise((resolve) => setTimeout(resolve, 10000))
                .then(() => {
                    this.getViewModel().setProperty("/reqGenerateDialogBusy", false);
                    oGenerateReqProgressIndicator.setVisible(false);
                });
            // this.getViewModel().setProperty("/reqGenerateDialogBusy", false);
            this.oDataHandler.generateReqWithAI(oPayload).then((oResult) => {
                oEvent.getSource().getParent().close();
                this.oComponent.oGeneratedReq = {
                    short_text: oPayload.REQUIREMENT_TITLE,
                    priority_v2: 30,
                    ex_project_guid: oPayload.PROJECT_ID,
                    ex_company_id: oPayload.SCOPE_ID,
                    ex_company_name: oPayload.SCOPE_NAME,
                    type_id_v2: oConstant.TASK_MASTER_TYPE.requirement,
                    to_texts: [{
                        text: oResult.DESCRIPTION,
                        text_type: "CIPTASKDES",
                        language: "en"
                    }],
                    to_entity_relations: [DataUtil.fromCrossDomainProcess2Process(oReqGenerationModel.getProperty("/solutionProcess"))],
                    to_tag_assignments: [],
                    to_iam_actions: []
                };
                this.oRouter.navTo(oConstant.ROUTE_TASK_CREATION, {
                    typeId: oConstant.ROUTE_ARGS_TASK_TYPE.requirement
                }, false);
            }).catch((error) => {
                this.handleTaskOperationError(error);
            }).finally(() => {
                this.getViewModel().setProperty("/reqGenerateDialogBusy", false);
            });
        },

        onCreateTaskOptionSelect: function (oEvent) {
            const oOption = oEvent.getSource().getBindingContext(oConstant.JSON_MODELS.createTaskOptionsModel).getObject();
            if (oOption.id !== oConstant.CREATE_FROM_TEMPLATE_OPTION) {
                this._oCreateTaskOptionPopover?.close();
                this._handleCreateItemClicked(oOption.routeArg);
                return;
            }

            this.setModel(oCreationOptionHandler.initTemplateSelectionModel(), oConstant.JSON_MODELS.templateSelectionModel);
            this._setupPopover(oEvent.getSource(), this._oTemplateSelectionPopover, this.pagePrefix(), "createTask.TemplateSelectionPopover", null)
                .then((oPopover) => {
                    this._oTemplateSelectionPopover = oPopover;
                });

            const isKanbanView = this.getViewModel().getProperty(DISPLAYED_VIEW) === oConstant.LIST_VIEW_TYPE.kanban;
            oCreationOptionHandler.buildTemplateSelectionModel(this.oCalmContextModel.getProperty(SELECTED_PROJECT_GUID), this.projectRelatedInfo.taskTypeFilter.selectedIds, this.oDataHandler, this.oBundle, this.oCalmContextModel.getProperty("/tileInfo/id"), isKanbanView)
                .then((oModel) => this.setModel(oModel, oConstant.JSON_MODELS.templateSelectionModel));
        },

        onTaskTemplateTypeSegmentChanged: function () {
            oCreationOptionHandler.setTemplates4ShowInModel(this.getModel(oConstant.JSON_MODELS.templateSelectionModel));
        },

        onTaskTemplateSelected: function (oEvent) {
            this._oTemplateSelectionPopover?.close();
            this._oCreateTaskOptionPopover?.close();
            const oTask = oEvent.getSource().getSelectedItem().getBindingContext(oConstant.JSON_MODELS.templateSelectionModel).getObject();
            this.createTaskFromTemplate(oTask);
        },

        // -------- Task List Process Hierarchy Info Popover Starts--------

        setInfoIconVisible: function (oEvent) {
            const isHovered = oEvent.getSource().getHovered();
            const oIcon = oEvent.getSource().getItems()[1]; // adapt if template structure changes
            if (isHovered) {
                oIcon.setVisible(true);
            } else if (!ICON_ID_TO_POPOVER.get(oIcon.getId())?.isOpen()) {
                oIcon.setVisible(false);
            }
        },

        // TODO: Refactor into its own file
        showProcessHierarchyPopover: async function (oEvent) {
            const oIcon = oEvent.getSource();
            const oTask = oIcon.getBindingContext().getObject();
            const aNodeIds = oTask.to_entity_relations
                .filter((r) => r.foreign_entity_type === oConstant.CALM_FOREIGN_TYPE.processHierarchy)
                .map((r) => r.foreign_entity_id);
            const aIdFilters = FilterHandler.constructProcessHierarchyFilterByIds(aNodeIds);

            this.setModel(this.oDataHandler.initProcessHierarchyServiceModel(), oConstant.ODATA_MODELS.crossPMHierarchy);
            if (!ICON_ID_TO_POPOVER.get(oIcon.getId())) {
                const oPopover = await this._setupPopover(oEvent.getSource(), null, `${oIcon.getId()}-fIdProcessHierarchyInfoPopover`, "crossDomain.ProcessHierarchyInfoPopover", null);
                oPopover.attachAfterClose(() => oIcon.setVisible(false));
                ICON_ID_TO_POPOVER.set(oIcon.getId(), oPopover);
            } else {
                this._setupPopover(oEvent.getSource(), ICON_ID_TO_POPOVER.get(oIcon.getId()), "fIdProcessHierarchyInfoPopover", "crossDomain.ProcessHierarchyInfoPopover", null);
            }

            // Filtering
            const oItemsBinding = ICON_ID_TO_POPOVER.get(oIcon.getId()).getContent()[0].getBinding("items");
            if (!oItemsBinding.isSuspended()) {
                oItemsBinding.suspend();
            }
            oItemsBinding.filter(aIdFilters);
            oItemsBinding.refresh();
            oItemsBinding.resume();
        },

        // -------- Task List Process Hierarchy Info Popover Ends --------

        // -------- Task List Status Popover Starts--------

        /**
         * Open a status popover on task list table to update main status
         * @param {import("sap/m/Button").Button$PressEvent} oEvent The button pressed event
         */
        changeStatus: async function (oEvent) {
            this._mainStatusPopover ??= new MainStatusPopover(this);
            const trigger = oEvent.getSource();
            const { statusPropertyName, selectedStatusId } = await this._mainStatusPopover.openBy(trigger);

            this._updateTaskStatus(trigger, statusPropertyName, selectedStatusId);
        },

        /**
         * Open a status popover on task list table to update sub status
         * @param {import("sap/m/Button").Button$PressEvent} oEvent The button pressed event
         */
        changeSubStatus: async function (oEvent) {
            this._subStatusPopover ??= new SubStatusPopover(this);
            const trigger = oEvent.getSource();
            const { statusPropertyName, selectedStatusId } = await this._subStatusPopover.openBy(trigger);

            this._updateTaskStatus(trigger, statusPropertyName, selectedStatusId);
        },

        /**
         *
         * @param {import('sap/m/Button').default} trigger
         * @param {string} statusPropertyName
         * @param {string} selectedStatusId
         */
        _updateTaskStatus: function (trigger, statusPropertyName, selectedStatusId) {
            this.getViewModel().setProperty("/busy", true);

            /** @type {import('sap/ui/model/odata/v4/Context').default} } */
            (trigger.getBindingContext())
                .setProperty(statusPropertyName, selectedStatusId)
                .then(() => {
                    this._showUpdateToastForStatus(selectedStatusId);
                    this.oEventBus.publish(this.oComponent.EVENT_CHANNEL, "taskUpdated", { updatedProperty: statusPropertyName, updatedNewValue: selectedStatusId });
                    this.resetItemLoadedFlag();
                })
                .catch((error) => this.handleTaskOperationError(error))
                .finally(() => this.getViewModel().setProperty("/busy", false));
        },

        // -------- Task List Status Popover Ends --------

        onNavToSolutionProcessInList: function (oEvent) {
            const oRelation = oEvent.getSource().getBindingContext().getObject();
            if (!oRelation?.ID) {
                return; // do nothing if no PM relation exist
            }
            NavigationHandler.navToSolutionProcess(oRelation.ID);
        },

        renderExternalReferenceCell: function (sId, oContext) {
            if (sId.endsWith("-0")) {
                const oFirstExternalRef = oContext.getObject();
                return oFirstExternalRef.url
                    ? new Link({ text: oFirstExternalRef.name, target: "_blank", href: oFirstExternalRef.url })
                    : new Text({ text: oFirstExternalRef.name });
            }
            return new InvisibleText({ text: this.oBundle.getText("invisibleTextItemEmpty") });
        }
    });
});
