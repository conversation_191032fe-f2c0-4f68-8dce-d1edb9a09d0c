<core:FragmentDefinition id="generateReqDialogFragment"
                         xmlns="sap.m"
                         xmlns:core="sap.ui.core">
    <Dialog id="generateReqDialog"
            title="{i18n>generateReqDialogTitle}"
            busy="{viewModel>/reqGenerateDialogBusy}"
            contentWidth="400px">
        <content>
            <VBox class="sapUiMediumMarginBegin"
                  width="80%"
                  renderType="Bare">
                <ObjectIdentifier title="{i18n>context}"
                                  class="sapUiSmallMarginTop sapUiSmallMarginBottom" />
                <!-- Scope -->
                <VBox class="sapUiTinyMarginBottom">
                    <Label text="{i18n>companyNew}"
                           labelFor="idGenReqScopeSelect"
                           showColon="true"
                           required="true" />
                    <Select id="idGenReqScopeSelect"
                            change="onScopeSelectChange"
                            items="{path:'calmContext>/selectedProject/to_companies', sorter:{path:'company_name',comparator:'.sorter.alphabetSorterWithPriority'}, templateShareable:false}"
                            selectedKey="{reqGenerationModel>/scopeId}"
                            width="100%">
                        <core:Item id="idScopeSelectItem"
                                   key="{calmContext>company_id}"
                                   text="{path: 'calmContext>company_name', type: '.UnescapedString'}" />
                    </Select>
                </VBox>
                <!-- Solution Processes -->
                <VBox class="sapUiTinyMarginBottom">
                    <Label text="{i18n>solutionProcess}"
                           labelFor="generateReqProcessInput"
                           showColon="true"
                           required="true" />
                    <Input id="generateReqProcessInput"
                           value="{path: 'reqGenerationModel>/solutionProcess/title', type:'.UnescapedString'}"
                           showValueHelp="true"
                           valueHelpRequest="onSolutionProcessValueHelpPress" />
                </VBox>
                <!-- Documents -->
                <VBox class="sapUiTinyMarginBottom">
                    <Label text="{i18n>documents}"
                           labelFor="idDocumentMI"
                           showColon="true"
                           required="true" />
                    <MultiInput id="idDocumentMI"
                                ariaLabelledBy="generateReqDialogDocLabel"
                                showValueHelp="true"
                                valueHelpRequest="onDocumentValueHelpRequest"
                                showClearIcon="true"
                                tokenUpdate="onDocumentTokenUpdate"
                                change="onDocumentChange"
                                showSuggestion="false">
                    </MultiInput>
                </VBox>
            </VBox>
            <VBox class="sapUiMediumMarginBegin"
                  width="80%"
                  renderType="Bare">
                <ObjectIdentifier title="{i18n>details}"
                                  class="sapUiSmallMarginTop sapUiSmallMarginBottom" />
                <!-- Title -->
                <VBox class="sapUiTinyMarginBottom">
                    <Label text="{i18n>title}"
                           labelFor="idTitleInput"
                           showColon="true"
                           required="true" />
                    <Input id="idTitleInput"
                           value="{reqGenerationModel>/requirementTitle}"
                           maxLength="255"
                           valueLiveUpdate="true" />
                </VBox>
                <!-- Requirement Template -->
                <VBox class="sapUiTinyMarginBottom">
                    <Label text="{i18n>requirementTemplate}"
                           labelFor="idReqTemplateSelect"
                           showColon="true" />
                    <Select id="idReqTemplateSelect"
                            items="{path:'reqGenerationModel>/templates', sorter:{path:'short_text',comparator:'.sorter.alphabetSorterWithPriority'}, templateShareable:false}"
                            selectedKey="{reqGenerationModel>/templateRequirementId}"
                            forceSelection="true"
                            width="100%">
                        <core:Item id="idTemplateItem"
                                   key="{reqGenerationModel>guid}"
                                   text="{path: 'reqGenerationModel>short_text', type: '.UnescapedString'}">
                        </core:Item>
                    </Select>
                </VBox>
            </VBox>
            <VBox class="sapUiMediumMarginBegin sapUiTinyMarginBottom"
                  width="80%"
                  renderType="Bare">
                <!-- Enable help doc search -->
                <ObjectIdentifier title="{i18n>advancedContext}"
                                  class="sapUiSmallMarginTop sapUiSmallMarginBottom" />
                <HBox alignItems="Center"
                      class="sapUiTinyMarginBottom">
                    <Switch customTextOff=" "
                            customTextOn=" "
                            change="onEnableHelpDocChanged"
                            state="{reqGenerationModel>/enableSapHelpDocumentation}"
                            class="padding-left-minus-0-5rem">
                    </Switch>
                    <Text text="{i18n>enableHelpDocText}" />
                </HBox>
            </VBox>
        </content>
        <beginButton>
            <Button type="Emphasized"
                    text="{i18n>generate}"
                    icon="sap-icon://ai"
                    press="onGenerateBtnPress" />
        </beginButton>
        <endButton>
            <Button text="{i18n>cancelBtn}"
                    press="onReqDeleteBtnCancel" />
        </endButton>
    </Dialog>
</core:FragmentDefinition>