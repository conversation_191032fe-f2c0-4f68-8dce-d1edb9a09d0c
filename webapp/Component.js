sap.ui.loader.config({
    paths: {
        "com/sap/calm/x/shared/ui": sap.ui.require.toUrl("com/sap/calm/imp/tkm/ui/resources/com/sap/calm/x/shared/ui/")
    }
});
/* eslint-env es6 */
/* eslint-disable max-params */
/* eslint-disable dot-notation */
/* eslint-disable camelcase */
sap.ui.define([
    "sap/ui/core/UIComponent",
    "com/sap/calm/imp/tkm/ui/model/UnifiedBundle",
    "com/sap/calm/imp/tkm/ui/model/Constant",
    "com/sap/calm/imp/tkm/ui/model/ConstantModels",
    "com/sap/calm/imp/tkm/ui/model/Models",
    "com/sap/calm/imp/tkm/ui/model/project/Project",
    "com/sap/calm/imp/tkm/ui/handler/ErrorHandler",
    "com/sap/calm/imp/tkm/ui/handler/DataHandler",
    "com/sap/calm/imp/tkm/ui/handler/DwcHandler",
    "com/sap/calm/imp/tkm/ui/handler/filter/DynamicFilterOptionHandler",
    "com/sap/calm/imp/tkm/ui/polyfills/PromisePolyfill",
    "sap/ui/model/json/JSONModel",
    "sap/ushell/Container"
], /**
 * @param {typeof import('sap/ui/core/UIComponent').default} UIComponent
 * @param {typeof import('sap/ui/model/json/JSONModel').default} JSONModel
 * @param {typeof import('sap/ushell/Container').default} ShellContainer
 * @param {typeof import('com/sap/calm/imp/tkm/ui/model/project/Project').default} ProjectModel
 * @param {typeof import('com/sap/calm/imp/tkm/ui/model/UnifiedBundle').default} UnifiedBundle
 * @param {typeof import('com/sap/calm/imp/tkm/ui/model/Constant').default} Constant
 * @param {typeof import('com/sap/calm/imp/tkm/ui/model/ConstantModels')} ConstantModels
 * @param {typeof import('com/sap/calm/imp/tkm/ui/handler/DwcHandler').default} DwcHandler
 * @param {typeof import('com/sap/calm/imp/tkm/ui/handler/DataHandler').default} DataHandler
 * @param {typeof import('com/sap/calm/imp/tkm/ui/polyfills/PromisePolyfill').default} PromisePolyfill
 */
function (UIComponent, UnifiedBundle, Constant, ConstantModels, Models, ProjectModel, ErrorHandler, DataHandler, DwcHandler, DynamicFilterOptionHandler, PromisePolyfill, JSONModel, ShellContainer) { // NOSONAR
    "use strict";

    const constant = Constant.getInstance();

    return UIComponent.extend("com.sap.calm.imp.tkm.ui.Component", {

        metadata: {
            manifest: "json"
        },

        EVENT_CHANNEL: constant.EVENT_CHANNEL,

        init: async function () {
            this.oBundle = UnifiedBundle.getInstance();
            this.oModelsData = new Models(this);
            this.tileInfo = this.oModelsData.getTileInfo(this.getComponentData());
            this._adaptComponentTitle();

            if (window.dwc && window.dwc?.toggles?.isActive("imp_tkm_adaptationPoC_toggle")) {
                const oManifest = this.getManifest();
                oManifest["sap.ui5"].flexEnabled = true;
            }

            // call the base component's init function
            UIComponent.prototype.init.apply(this, arguments);

            this.oErrorHandler = new ErrorHandler(this);// make sure called after component inti which create router of component
            this.oDataHandler = new DataHandler(this);

            // set the device model
            this.setModel(this.oModelsData.createDeviceModel(), "device");
            this.setModel(new JSONModel(constant.TASK_ATTR_VISIBILITY_MATRIX), "visibilityMatrix");
            this.setModel(new JSONModel({ checklistEditableFlag: true }), constant.JSON_MODELS.globalViewModel);
            this.setModel(new JSONModel({ tileInfo: this.tileInfo }), constant.JSON_MODELS.calmContext);

            const sComponentId = this.getId();
            if (!DwcHandler.isCalmContextExist() && constant.PTM_COMPONENT_IDS.includes(sComponentId)) {
                // We also check component id above because we don't want to come in here when running OPA
                // await sap.ui.getCore().loadLibrary("com.sap.calm.imp.globalcontext", { async: true });
                await com.sap.calm.imp.globalcontext.setup();
                this._loadCalmContext();
            } else {
                this._loadCalmContext();
            }

            // polyfill PromiseWithResolver
            // Only required for tests, so remove once e2e and OPA properly support this
            PromisePolyfill.polyfillPromiseWithResolvers();
        },

        setFavoriteTasksModel: async function () {
            let aFavoriteTaskGUIDs = [];
            const oCurrentComponenent = this;
            try {
                const oPersonalizationService = await ShellContainer.getServiceAsync("PersonalizationV2");
                oCurrentComponenent.oPersonalizer = await oPersonalizationService.getPersonalizer({
                    container: constant.EVENT_CHANNEL,
                    item: constant.PERSONALIZATION_ITEM
                }, {
                    keyCategory: oPersonalizationService.constants.keyCategory.FIXED_KEY,
                    writeFrequency: oPersonalizationService.constants.writeFrequency.LOW,
                    clientStorageAllowed: false
                }, oCurrentComponenent);
                const oPersData = await oCurrentComponenent.oPersonalizer.getPersData();
                aFavoriteTaskGUIDs = oPersData || [];
            } catch (error) {
                oCurrentComponenent.oPersonalizer = null;
            } finally {
                oCurrentComponenent.setModel(new JSONModel(aFavoriteTaskGUIDs), constant.JSON_MODELS.favoriteTasks);
            }
        },

        setCiasSystemUrl: async function () {
            let sUrl;
            try {
                const oCommonDataService = await ShellContainer.getServiceAsync("CommonDataModel");
                const aVisualizations = await oCommonDataService.getVisualizations();
                const CIAS_TILE_NAME = "cloud-integration-atomation-service";
                sUrl = aVisualizations[CIAS_TILE_NAME] ? aVisualizations[CIAS_TILE_NAME].vizConfig["sap.flp"].target.url : "";
            } catch (error) {
                sUrl = "";
            }
            this.ciasUrl = sUrl;
        },

        setGlobalViewModelProperty: function (sPropertyPath, sPropertyValue) {
            this.getModel(constant.JSON_MODELS.globalViewModel).setProperty(sPropertyPath, sPropertyValue);
        },

        _adaptComponentTitle: function () {
            const oManifest = this.getManifestObject();
            if (oManifest._oManifest["sap.app"]) {
                oManifest._oManifest["sap.app"].title = this.oBundle.getText(this.tileInfo.titleTextKey);
            }
        },

        _loadCalmContext: function () {
            const oRouter = this.getRouter();
            const oParams = this._buildCalmContextRequestParams();
            if (!oParams.selectedProjectGuid) {
                oRouter.getTargets().display("noAuthority");
                return;
            }

            // dispatch route before calm context ready so that task list page will be rendered earlier
            oRouter.initialize();
            this.dispatchRoute();

            // but task list request relies on calm context, so we need to use promise to make sure request is issued after calm context ready
            let calmContextReadyPromiseResolve;
            this.calmContextReadyPromise = new Promise((resolve) => {
                calmContextReadyPromiseResolve = resolve;
            });
            this.oDataHandler.getCalmContext(oParams.selectedProjectGuid).then((aContexts) => {
                this.setupCalmContext(oParams, aContexts);
                calmContextReadyPromiseResolve();
            });
            this.setFavoriteTasksModel();
            this.buildComponentConstantModel();
            this.setCiasSystemUrl();
        },

        _buildCalmContextRequestParams: function () {
            this.oStartupParams = this.getStartupParameters();
            let sSelectedProjectGuid = DwcHandler.getSelectedProjectGuid();
            const oFeatureToggle = {};
            DwcHandler.getActiveFeatures().forEach((sActivateFeature) => {
                oFeatureToggle[sActivateFeature] = true;
            });

            if (this.oStartupParams?.projectGuid) {
                sSelectedProjectGuid = DwcHandler.getSelectedProjectGuid(this.oStartupParams.projectGuid);
            }

            return {
                selectedProjectGuid: sSelectedProjectGuid,
                featureToggle: oFeatureToggle
            };
        },

        setupCalmContext: function (oRequestParams, aContexts) {
            const INDEX_CURRENT_USER_INFO = 0;
            const INDEX_SELECTED_PROJECT = 1;
            const INDEX_AUTHORIZATION = 2;
            // Including deliverables, releaseVersions, projectRoles, products and sources and solution processes.
            const INDEX_DYNAMICFILTERS = 3;
            const INDEX_WORKSTREAMS = 4;
            const INDEX_ALL_PROJECTS = 5;
            const INDEX_CBC_CONFIGURATION = 6;
            const INDEX_AVAILABLE_SOLUTIONS = 7;
            const INDEX_TAGS = 8;
            const INDEX_CONTENT_PACKAGE = 9;
            const INDEX_PROJECT_AUTH = 10;
            const INDEX_TASK_COUNT_BY_PHASE = 11;
            const INDEX_TASK_COUNT_BY_RELEASE_VERSION = 12;
            const INDEX_ALL_DELIVERABLES = 13;

            const oTaskAuthorization = this._parseTaskAuthorizationResponse(aContexts[INDEX_AUTHORIZATION],
                aContexts[INDEX_PROJECT_AUTH],
                aContexts[INDEX_SELECTED_PROJECT].guid);

            const oProject = aContexts[INDEX_SELECTED_PROJECT];
            if (oProject.to_cbcProjects !== undefined && oProject.to_cbcProjects.length > 0) {
                this.oDataHandler.refreshCBCProjectTasks(oProject.guid);
            }
            const oCalmContextContent = {
                allProjects: aContexts[INDEX_ALL_PROJECTS],
                selectedProject: oProject,
                userInfo: {
                    userId: aContexts[INDEX_CURRENT_USER_INFO].getId(),
                    userName: aContexts[INDEX_CURRENT_USER_INFO].getFullName(),
                    email: aContexts[INDEX_CURRENT_USER_INFO].getEmail()
                },
                authTask: oTaskAuthorization.authorization,
                featureToggle: oRequestParams.featureToggle,
                universalIdMap: DynamicFilterOptionHandler.parseUniversalIdMap(aContexts[INDEX_DYNAMICFILTERS]),
                filterOptions: DynamicFilterOptionHandler.parseDynamicFilterOptions(aContexts[INDEX_DYNAMICFILTERS]),
                workstreams: this.oModelsData.formatWorkstreams(aContexts[INDEX_WORKSTREAMS].value),
                cbcConfiguration: aContexts[INDEX_CBC_CONFIGURATION],
                activateSolutions: aContexts[INDEX_AVAILABLE_SOLUTIONS],
                tags: aContexts[INDEX_TAGS],
                contentPackages: aContexts[INDEX_CONTENT_PACKAGE],
                tileInfo: this.tileInfo,
                taskCountByPhase: aContexts[INDEX_TASK_COUNT_BY_PHASE],
                taskCountByReleaseVersion: aContexts[INDEX_TASK_COUNT_BY_RELEASE_VERSION],
                allDeliverables: this.oModelsData.getDeliverablesByProjectMilestones(aContexts[INDEX_ALL_DELIVERABLES], oProject.to_milestones)
            };
            this.getModel(constant.JSON_MODELS.calmContext).setData(oCalmContextContent);
            this._initProject(oProject);
        },

        _initProject(oProject) {
            ProjectModel.init(oProject);
        },

        _parseTaskAuthorizationResponse: function (oTaskAuthContext, oProjectAuthContext, sSelectedProjectGuid) {
            const oOverallTaskAuthorization = {};
            let oTaskAuthorization = {};

            oTaskAuthContext.value.forEach((oTaskAuth) => {
                const oProjectAuth = oProjectAuthContext.value.find((item) => item.PROJECT_GUID === oTaskAuth.PROJECT_GUID);
                const oAuth = this._constructAuthorization(oTaskAuth, oProjectAuth);
                oOverallTaskAuthorization[oTaskAuth.PROJECT_GUID] = oAuth;

                // Get task authorization of current selected project.
                if (sSelectedProjectGuid === oTaskAuth.PROJECT_GUID) {
                    oTaskAuthorization = oAuth;
                }
            });

            this.setModel(new JSONModel(oOverallTaskAuthorization), constant.JSON_MODELS.overallTaskAuth);

            return {
                authorization: oTaskAuthorization
            };
        },

        _constructAuthorization: function (oTaskAuth, oProjectAuth) {
            return {
                hasCreateAuth: oTaskAuth.HAS_CREATE_AUTH || false,
                hasDeleteAuth: oTaskAuth.HAS_DELETE_AUTH || false,
                hasDisplayAuth: oTaskAuth.HAS_DISPLAY_AUTH || false,
                hasEditAuth: oTaskAuth.HAS_EDIT_AUTH || false,
                hasProjectEditAuth: oProjectAuth?.HAS_EDIT_AUTH || false,
                isAdmin: oTaskAuth.IS_ADMIN || false,
                isProjectLead: oProjectAuth?.IS_PROJECT_LEAD || false
            };
        },

        exit: function () {
            this.oErrorHandler.destroy(true);
            this.oDataHandler.destroy(true);
        },

        dispatchRoute: async function () {
            const oRouter = this.getRouter();
            const oMatchedConfiguredRoute = oRouter.getRouteInfoByHash(oRouter.getHashChanger().getHash()) || {};

            const oInternalParams = await this._buildInternalParameters(this.oStartupParams, oMatchedConfiguredRoute);
            oRouter.navTo(oInternalParams.route, oInternalParams, undefined, true);
        },

        /**
         * Build parameters for internal navigation
         * @param {Object} rawParams : url params
         * @param {Object} oMatchedConfiguredRoute : route info for configured internally
         * @returns {Promise<Object> }
         */
        _buildInternalParameters: async function (rawParams, oMatchedConfiguredRoute) {
            // startup parameter route as first priority, and map it if necessary
            let internalRoute = constant.EXT_ROUTE_2_INTER[rawParams.route] || rawParams.route || oMatchedConfiguredRoute.name || constant.ROUTE_TASK_LIST;
            const internalTypeId = constant.MAP_TASK_TYPE_TO_ARGS[rawParams.type] || constant.MAP_TASK_TYPE_TO_ARGS[constant.TASK_MASTER_TYPE.projectTaskV2];
            let internalObjectId = rawParams.objectId || oMatchedConfiguredRoute?.arguments?.objectId;

            // process route for internal navigation
            if (constant.TASK_DETAIL_COMPATIBLE_ROUTES.includes(oMatchedConfiguredRoute.name) && constant.ROUTE_TASK_DETAIL === internalRoute) {
                //  if compatible, use compatible route
                internalRoute = oMatchedConfiguredRoute.name;
            }

            // build route for external navigate into internal
            if (Object.keys(constant.EXT_ROUTE_2_INTER).includes(rawParams.route)) {
                if (constant.CALM_RELATION_ROUTE.creation === rawParams.route) {
                    // for cross domain creation case, at the first time, it should not contain oMatchedConfiguredRoute in the URL
                    // but after some navigation internally, the URL will contain oMatchedConfiguredRoute, we should ignore cross domain creation for back navigation
                    if (oMatchedConfiguredRoute.name && oMatchedConfiguredRoute.name !== constant.ROUTE_TASK_CREATION) {
                        internalRoute = oMatchedConfiguredRoute.name;
                    } else {
                        this.sCreatePageOpenFrom = rawParams.fType || "others";
                    }
                } else {
                    //  get objectId for display relation ( entity created from external)
                    internalObjectId = await this._getTargetGuid();
                    rawParams.objectId = internalObjectId;
                }
            }

            const oNavParams = {
                route: internalRoute,
                objectId: internalObjectId,
                typeId: internalTypeId,
                viewType: rawParams?.viewType,
                pageMode: rawParams?.pageMode || oMatchedConfiguredRoute?.arguments?.pageMode,
                // case 1: triggered from notification item which contains 'commentId' as startup parameter
                // case 2: access by url directly which contains specific 'commentId'
                "?query": this.getQueryParams(rawParams?.commentId ? rawParams : oMatchedConfiguredRoute?.arguments?.["?query"])
            };

            return oNavParams;
        },

        getQueryParams: function (oQueryParams) {
            if (!oQueryParams) {
                return;
            }

            if (oQueryParams?.subTaskId) {
                return { // for open sub task's comments
                    subTaskId: oQueryParams?.subTaskId
                };
            }
            if (oQueryParams?.commentId) {
                return { // open one specific comment
                    commentId: oQueryParams?.commentId
                };
            }
        },

        /**
         * When internal create external entity then nav back to internal,
         * backend will not guarantee relation is ready, so frontend will build relation if needed for consistency
         * case sId: Requirement guid when create feature and back
         * @returns {Promise <String> } guid of task/us/requirement
         */
        _getTargetGuid: function () {
            // CA is using objectId to open specific task detail page
            if (this.oStartupParams.objectId) {
                return Promise.resolve(this.oStartupParams.objectId);
            }
            // cross domain navigation is using sId to open specific task detail page for postCreateEntityWithRelation
            if (this.oStartupParams.sId) {
                return Promise.resolve(this.oStartupParams.sId);
            }
            // standard way
            this.oRelationIdParsePromise = this.oDataHandler.getCalmRelationEntityId(this.oStartupParams.relId);
            return this.oRelationIdParsePromise;
        },

        getStartupParameters: function () {
            const oComponentData = this.getComponentData() || {};
            const oStartupParams = oComponentData.startupParameters || {};
            const oResult = {};
            Object.keys(oStartupParams).forEach((item) => {
                if (constant.NAV_PARAMETERS_MULTI_SUPPORTED_FILTER.indexOf(item) >= 0 && oStartupParams[item].length > 0) {
                    oResult[item] = oStartupParams[item];
                } else if (oStartupParams[item].length > 0) {
                    oResult[item] = oStartupParams[item][0];
                }
            });
            return oResult;
        },

        getContentDensityClass: function () {
            // Get content density of FLP, iff FLP not set then set default value "sapUiSizeCompact"
            return ShellContainer.getUser().getContentDensity() || "sapUiSizeCompact";
        },

        buildComponentConstantModel: function () {
            // Story points model
            this.setModel(new JSONModel(this.oModelsData.getStoryPoints()), constant.JSON_MODELS.storyPoints);

            // Priority model
            this.setModel(new JSONModel(this.oModelsData.getTaskPriorities()), constant.JSON_MODELS.taskPriority);

            // User Type model
            this.setModel(new JSONModel(this.oModelsData.getUserTypes()), constant.JSON_MODELS.userTypes);

            // Status and sub status models
            this.setModel(new JSONModel(this.oModelsData.getTaskStatuses()), constant.TASK_STATUS_MODEL_NAME);
            this.setModel(new JSONModel(this.oModelsData.getUserStoryStatuses()), constant.USER_STORY_STATUS_MODEL_NAME);
            this.setModel(new JSONModel(ConstantModels.REQUIREMENT_MAIN_STATUS), constant.REQUIREMENT_STATUS_MODEL_NAME);
            this.setModel(new JSONModel(this.oModelsData.getDefectStatuses()), constant.DEFECT_STATUS_MODEL_NAME);
            this.setModel(new JSONModel(this.oModelsData.getQualityGateStatuses()), constant.QGATE_STATUS_MODEL_NAME);
            this.setModel(new JSONModel(ConstantModels.REQUIREMENT_SUB_STATUS), constant.REQUIREMENT_SUB_STATUS_MODEL_NAME);
            this.setModel(new JSONModel(this.oModelsData.getDefectSubStatuses()), constant.DEFECT_SUB_STATUS_MODEL_NAME);
            this.setModel(new JSONModel(this.oModelsData.getQualityGateSubStatuses()), constant.QGATE_SUB_STATUS_MODEL_NAME);

            // "notShownMsgAgainCheckBox" model for delete confirm dialog
            this.setModel(new JSONModel({
                selected: false
            }), constant.JSON_MODELS.noMsgAgainCheckBox);
        }
    });
});
